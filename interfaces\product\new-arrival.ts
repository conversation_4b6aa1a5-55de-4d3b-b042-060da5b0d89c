export interface NewArrival {
	hasStock: boolean
	productId: number
	description: string
	media: Media
	type: string
	colors: string[]
	isListed: boolean
	variance: Variance
	brandId: number
	minPrice: Price
	name: string
	avgRate: number
	maxPrice: Price
	SKU: string
	brand: string
	slug?: string
	hasOffer: boolean
}

export interface MaxPrice {
	symbol: string
	currency: string
	currencyId: number
	value: number
}
export interface Price {
	symbol: string
	currency: string
	currencyId: number
	value: number
}

export interface Media {
	cover: Cover[]
	gallery: Cover[]
}

export interface Cover {
	preview: string
	disk: string
	src: string
	fileSize: number
	id: number
	mimeType: string
	sort: number
}

export interface Variance {
	varianceId: number
	brandId: number
	name: string
	media: Media
	SKU: string
	type: string
	stock: Stock
}

export interface Stock {
	note: string
	quantity: number
	supplierId: number
	isOffer: boolean
	price: Price
	stockId: number
	isPreOrder: boolean
	maxPerUser: number
	priceBeforeOffer?: MaxPrice
}
