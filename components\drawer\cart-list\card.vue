<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'
import type { UseProductDetails } from '~/interfaces/product/details'
import type { Items } from '~/interfaces/cart/cart-list'

const { product, viewOnly } = defineProps<{
	product: Items
	viewOnly?: boolean
}>()

interface Payload {
	quantity?: number
	productId?: number
	varianceId?: number
	bundleId?: unknown
}

const emit = defineEmits<{
	(event: 'remove:cart', cartId: number): void
	(event: 'update:quantity', payload: Payload): void
}>()

const item = ref<UseProductDetails>(null)
const quantity = ref(1)

const image = computed(() => (item.value as UseProductDetails).media?.cover?.[0]?.src)

/** Mark item as removed and trigger 'remove:cart' event with cart ID. */
const onRemove = () => {
	emit('remove:cart', product.cartId)
}

/** Increment the item quantity if stock allows. */
const onIncrease = () => {
	if (quantity.value < item.value.variance.stock?.maxPerUser) {
		quantity.value += 1
		updateQuantity()
	}
}

/** Decrement the item quantity if greater than 1. */
const onDecrease = () => {
	if (quantity.value > 1) {
		quantity.value = quantity.value - 1
		updateQuantity()
	}
}

/**
 * Debounced function to update product quantity.
 * Triggers 'update:quantity' event after 1-second delay.
 */
const updateQuantity = useDebounceFn(async () => {
	emit('update:quantity', {
		quantity: quantity.value,
		productId: product.productId,
		varianceId: product.varianceId,
		bundleId: null,
	})
}, 1000)

onBeforeMount(() => {
	item.value = useProduct(product) as UseProductDetails
	quantity.value = product.quantity as number
})
</script>

<template>
	<div
		class="flex w-full border border-gray-200 rounded-lg overflow-hidden transition-all ease-in-out duration-300"
	>
		<div
			v-if="item"
			class="grid grid-cols-3 gap-1"
			:class="[viewOnly ? 'h-28' : 'h-36']"
		>
			<NuxtLinkLocale
				:to="`/product/${item.variance.slug}`"
				:disabled="viewOnly"
				class="flex h-full col-span-1 bg-gray-100 flex-col justify-center items-center overflow-hidden box-border p-4"
			>
				<div class="flex p-2 w-full h-full justify-center items-center">
					<NuxtImg
						:src="image"
						:alt="item.name"
						provider="backend"
						format="webp"
						class="object-contain min-w-28"
					/>
				</div>
			</NuxtLinkLocale>
			<div class="flex flex-col col-span-2 justify-evenly px-2 h-full overflow-hidden">
				<div class="flex w-full items-start justify-between">
					<NuxtLinkLocale
						:to="`/product/${item.variance.slug}`"
						class="truncate-2-line text-sm font-semibold"
					>
						{{ item.name }}
					</NuxtLinkLocale>
					<button
						v-if="!viewOnly"
						class="flex items-center"
						@click.prevent="onRemove"
					>
						<Icon
							name="lucide:trash-2"
							size="20px"
							class="text-gray-400"
						/>
					</button>
				</div>
				<NuxtLinkLocale
					class="flex w-full gap-2"
					:to="`/product/${item.variance.slug}`"
				>
					<span class="text-green-600 text-lg font-bold">{{ item.priceFormatted }}</span>
					<div class="flex justify-between items-center gap-2">
						<div class="flex items-start justify-between gap-4">
							<div class="flex flex-col">
								<div
									v-if="item.discountAmount"
									class="flex gap-2 items-center text-md"
								>
									<span class="text-gray-600 line-through">{{ item.offerPriceFormatted }}</span>
								</div>
							</div>

							<div
								v-if="!!item.discountPercent"
								class="flex"
							>
								<span class="bg-orange-400 text-white text-xs font-semibold px-4 py-1 rounded-full">
									{{ $t('product.card-discount', { amount: item.discountPercent }) }}
								</span>
							</div>
						</div>
					</div>
				</NuxtLinkLocale>

				<div
					v-if="!viewOnly"
					class="flex items-center gap-6"
				>
					<div
						id="price-section"
						class="grid grid-cols-[auto_1fr_auto] border border-gray-200 rounded max-w-44 items-center overflow-hidden"
					>
						<button
							class="border-e border-gray-200 w-7 items-center flex justify-center h-7"
							@click.prevent="onIncrease"
						>
							<Icon
								name="lucide:plus"
								height="12px"
								width="12px"
							/>
						</button>
						<input
							v-model="quantity"
							name="quantity"
							type="number"
							readonly
							class="no-spinner border-none outline-none col-span-1 w-auto max-w-10 text-center text-xs"
							:max="2"
						>
						<button
							class="border-s border-gray-200 w-7 h-full items-center flex justify-center"
							@click.prevent="onDecrease"
						>
							<Icon
								name="lucide:minus"
								width="12px"
							/>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
