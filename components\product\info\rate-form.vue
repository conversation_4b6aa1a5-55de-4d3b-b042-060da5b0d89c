<script setup lang="ts">
import { toast } from 'vue-sonner'
import { useAuthStore } from '~/store/useAuthStore.client'

const { productId } = defineProps<{
	productId?: number
}>()

const openForm = ref<boolean>(false)
const route = useRoute()
const { userSession, isLoggedIn } = useAuthStore()
const { t } = useI18n()
const userName = computed(() => isLoggedIn ? `${userSession?.firstName} ${userSession?.lastName}` : '')
const form = ref({
	rating: 0,
	description: '',
})

watch(route, () => {
	nextTick(() => openForm.value = route.query?.form === 'rating')
}, { immediate: true })

const isFormValid = computed<boolean>(() => form.value.rating > 0 && form.value.description.length > 2)
const submitReview = async () => {
	const { $api } = useNuxtApp()
	$api<unknown>(`products/${productId}/rating`, {
		method: 'POST',
		body: {
			rating: form.value.rating,
			review: form.value.description,
		},
	})
		.then(() => {
			toast.success(t('form.submit-rate-success'))

			form.value = {
				rating: 0,
				description: '',
			}
		})
		.catch((error) => {
			throw error
		})
}

const onWriteReview = () => {
	if (isLoggedIn) {
		openForm.value = true
		return
	}

	const route = useRoute()
	const router = useRouter()

	// Clone existing query and add `auth=login`
	const query = { ...route.query, auth: 'login' }

	// Redirect to the same path with the updated query
	router.push({
		path: route.path,
		query,
	})
}
</script>

<template>
	<div
		id="rating-form"
		class="flex flex-col w-full gap-3"
	>
		<div class="flex font-semibold text-base">
			{{ $t('product.write-review-title') }}
		</div>

		<div class="flex font-semibold text-sm text-gray-500">
			{{ $t('product.share-review-title') }}
		</div>

		<Button
			v-if="!openForm"
			class="max-w-40 mt-2"
			@click="onWriteReview"
		>
			{{ $t('product.write-review-btn-title') }}
		</Button>

		<div
			v-if="openForm"
			class="flex flex-col w-full gap-4"
		>
			<span class="text-sm text-gray-500">{{ $t('product.rate') }}</span>
			<div class="flex w-full gap-2">
				<button
					v-for="(_, index) in Array(5)"
					:key="`form-rating-${index}`"
					:class="form.rating >= 1+index ? 'text-rating-200' : 'text-rating-100'"
					@click.prevent="() => form.rating = 1+index"
				>
					<Icon
						name="ui:rate-star"
					/>
				</button>
			</div>

			<input
				:value="userName"
				:disabled="true"
				type="text"
				name="username"
				:placeholder="$t('form.full-name-placeholder')"
				class="border border-gray-300 rounded-md text-sm font-medium text-gray-500 h-10 p-3 outline-none"
			>

			<textarea
				v-model="form.description"
				name="description"
				:placeholder="$t('form.rate-description-placeholder')"
				class="border border-gray-300 rounded-md text-sm font-medium text-gray-500 p-3 outline-none resize-none"
				rows="5"
			/>

			<div class="flex w-full justify-end items-center">
				<Button
					v-if="isLoggedIn"
					class="w-36"
					:disabled="!isFormValid"
					:class="{ 'opacity-50 cursor-not-allowed': !isFormValid }"
					@click="submitReview"
				>
					{{ $t('form.submit-rate-title') }}
				</Button>
			</div>
		</div>
	</div>
</template>
