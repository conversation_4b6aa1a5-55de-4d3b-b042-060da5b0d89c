<script setup lang="ts">
import { useMediaQuery, useWindowSize } from '@vueuse/core'
import { useBreadcrumbs } from '~/composables/useBreadcrumbs'
import { useInfiniteScroll } from '~/composables/useInfiniteScroll'
import { useMetaData } from '~/composables/useMetaData'
import type { ProductList, Item, Pagination, Filters as CategoryFilters } from '~/interfaces/product/product-list'
import type { CategoryDetails } from '~/interfaces/category/details'
import Filters from '~/components/filter/index.vue'

const { height: screenHeight } = useWindowSize()

interface Category {
	name: string
	slug: string
	parent?: Category | null
}

const PER_PAGE = 24
const router = useRouter()
const route = useRoute()
const products = ref<Item[]>([])
const loadingMore = ref<boolean>(false)
const loadMoreBtnRef = ref<HTMLButtonElement | null>(null)
const isDesktop = useMediaQuery('(min-width: 600px)', { ssrWidth: 1000 })

/** Set SEO Data **/
const { setSeoData } = useMetaData({
	pageName: 'brands',
	fullPath: route.fullPath,
})
setSeoData()

const slug = computed(() => String(route.params.slug || ''))
const slugs = computed<string[]>(() => slug.value.split(','))
const activeSlug = computed(() => {
	if (Array.isArray(slugs.value)) {
		return [...slugs.value].pop()
	}

	return null
})
type QueryType = {
	page?: string
}
const query = computed<QueryType>(() => route.query)
/** Selected filters **/
const selectedFilters = ref<QueryType>(query.value)
const page = computed<number>(() => Number(query.value?.page || 1))

/** fetch details api **/
const {
	data: details,
	error: detailsError,
} = await useApi<CategoryDetails>(`/brands/${activeSlug.value}`, {
	watch: [activeSlug],
	deep: true,
	immediate: true,
})

const categoryDetails = computed(() => details.value as CategoryDetails)

/** In case of any error **/
if (detailsError.value) {
	console.error(`Error fetching category:${slug.value} details:`, detailsError.value)
}

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	const { buildBreadcrumbs } = useBreadcrumbs()
	return buildBreadcrumbs(details.value as Category, 'category')
})

const apiQuery = computed(() => {
	// in-case there are slugs and categories
	let queryText = null
	queryText = {
		brandId: activeSlug.value,
	}
	return {
		...queryText,
		orderBy: 'createdAt,desc',
		perPage: PER_PAGE,
		page: query.value?.['page'] || 1,
		...selectedFilters.value,
	}
})
/** fetch product api **/
const { data, error, status } = await useApi<ProductList>(`/search`, {
	query: apiQuery,
	watch: [apiQuery],
})
/** In case of any error **/
if (error.value) {
	console.error(`Error fetching category:${slug.value} products:`, error.value)
}

const result = computed(() => data.value as ProductList)
const pagination = computed(() => (data.value as ProductList)?.pagination as Pagination)

if (!import.meta.client) {
	products.value = (result.value as ProductList)?.items as Item[]
}
/** Watching on the data result **/
watch(data, () => {
	if (!result.value?.items || !import.meta.client) {
		return
	}

	if (!page.value || page.value == 1) {
		products.value = result.value?.items as Item[] || []
	} else {
		products.value = [...(products.value || []), ...(result.value?.items || [])] as Item[]
	}
	loadingMore.value = false
}, { immediate: true })

/** Loading enabled only on the open page **/
const loading = computed<boolean>(() => {
	return status.value === 'pending'
})

/** Disable Load More Btn **/
const disableLoadMore = computed<boolean>(() => {
	if (isEmptyState.value) {
		return true
	}
	return pagination.value?.lastPage <= Number(page.value)
})

/** Show Empty State **/
const isEmptyState = computed<boolean>(() => {
	return !products.value?.length && !products.value?.length && !loading.value
})

/** Next page link */
const nextPagePath = computed(() => {
	const query = new URLSearchParams({
		...route.query,
		page: String(Number(route.query?.page ?? 1) + 1),
	}).toString()

	return `${route.path}?${query}`
})
const title = computed(() => categoryDetails.value?.name)
const suggestion = computed(() => categoryDetails.value?.children)
const totalProducts = computed<number>(() => result.value?.pagination?.total || 0)
const filter = computed(() => result.value?.filters as CategoryFilters)

/**
 * Update the filter values accumulation
 * @param selected
 */
const onFilterUpdate = (selected: Record<string, string | string[] | null | undefined>) => {
	products.value = []
	status.value = 'pending'

	nextTick(() => {
		const filteredQuery = Object.fromEntries(
			Object.entries({
				...route.query,
				...selected,
				page: 1,
			}).filter(([_, v]) => Boolean(v)),
		)

		router.push({
			path: route.path,
			query: filteredQuery,
		})
		selectedFilters.value = filteredQuery
	})
}

/** Special loading status for the filters only **/
const pageLoading = computed(() => !!loading.value && !filter.value)

/** Card only loading in case there is loading and no products in a list **/
const cardLoading = computed(() => !!loading.value && !products.value?.length)

/** Get the root margin by 3dvh **/
const rootMargin = computed(() => ((screenHeight.value * 30) / 100) + 'px')

/** load more on infinite scroll */
useInfiniteScroll(loadMoreBtnRef, async () => {
	if (!disableLoadMore.value) {
		loadingMore.value = true
		await navigateTo(nextPagePath.value)
	}
}, rootMargin.value)
</script>

<template>
	<div
		class="grid grid-cols-4 gap-2 auto-rows-auto"
	>
		<Card class="col-span-4 max-sm:hidden rounded-t-none border-t-0">
			<Breadcrumb
				:links="breadCrumbLinks"
				:loading="pageLoading"
			/>
		</Card>
		<div class="col-span-1 max-sm:hidden">
			<Filters
				:filter="filter"
				:loading="pageLoading"
				:update="onFilterUpdate"
			/>
		</div>
		<div
			class="col-span-3 max-sm:col-span-4"
		>
			<Card class="flex flex-col">
				<CardHeader class="flex flex-col max-sm:!px-4">
					<div class="flex w-full justify-between items-center">
						<template v-if="pageLoading">
							<Skeleton class="h-7 w-32" />
						</template>
						<template v-else>
							<span class="text-md font-bold">{{ title }}</span>
						</template>
						<div class="flex">
							<FilterSorting
								v-if="isDesktop"
								:total="totalProducts"
								:update="onFilterUpdate"
								:loading="pageLoading"
							/>
							<FilterMobile
								v-else
								:total="totalProducts"
								:filter="filter"
								:loading="pageLoading"
								:update="onFilterUpdate"
							/>
						</div>
					</div>
					<CategoriesSuggestion
						:slug="slug"
						:categories="suggestion"
					/>
				</CardHeader>
				<CardContent class="grid gap-4 pb-6 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xs:px-4">
					<ProductCard
						v-for="product in products"
						:key="product.productId"
						:product="product"
					/>

					<template v-if="!!cardLoading">
						<ProductCard
							v-for="(_, index) in Array(PER_PAGE)"
							:key="`product.${_}${index}`"
							:loading="true"
						/>
					</template>

					<template v-if="isEmptyState">
						<div class="col-span-4 flex flex-col gap-4 justify-center items-center py-12">
							<Icon
								name="ui:empty-categories"
								class="h-80 w-80"
							/>
							<span class="text-lg font-bold">{{ $t('product.empty-list-text') }}</span>
						</div>
					</template>
				</CardContent>
				<CardFooter
					v-if="!disableLoadMore"
					class="flex items-center justify-center p-5"
				>
					<Button
						v-if="products?.length"
						variant="outline"
						:size="'lg'"
						as-child
						class="[&_svg]:size-10"
					>
						<LazyNuxtLink
							:disabled="loadingMore"
							:to="nextPagePath"
						>
							<template v-if="!loadingMore">
								{{ $t('categories.more-btn-title') }}
							</template>
							<template v-else>
								<Spinner />
							</template>
						</LazyNuxtLink>
						<span />
					</Button>
				</CardFooter>
				<div ref="loadMoreBtnRef" />
			</Card>
		</div>
	</div>
</template>
