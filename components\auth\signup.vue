<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import * as z from 'zod'
import type { AuthRegisterPayload } from '~/interfaces/auth/form'
import type FormPhoneValue from '~/interfaces/form'
import { useAuthStore } from '~/store/useAuthStore.client'

const authStore = useAuthStore()

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const passwordEye = ref(false)
const confirmEye = ref(false)

const { t } = useI18n()

/** validate form **/
const Form = useForm({
	validationSchema: toTypedSchema(
		z.object({
			firstName: z.string().min(1, t('error.required')),
			lastName: z.string().min(1, t('error.required')),
			password: z.string()
				.min(7, t('error.required'))
				.regex(/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/, {
					message: t('error.password-strength'),
				}),

			confirmPassword: z.string().min(7, t('error.required')),
			phone: z.object({
				number: z.string().min(5, t('error.required')),
				iso: z.string(),
				code: z.string(),
				isValid: z.boolean(),
			})
				.refine(phone => phone.isValid, t('error.phone-number-invalid')),
		}),
	),

	initialValues: toRaw({
		firstName: '',
		lastName: '',
		password: '',
		confirmPassword: '',
		phone: {
			code: '962',
			iso: 'JO',
			number: '',
			isValid: false,
		},
	}),
})

/** Signup user to app */
const doSignUp = Form.handleSubmit(async (values) => {
	loading.value = true

	await authStore.signup({
		password: values.password,
		phone: values.phone,
		firstName: values.firstName,
		lastName: values.lastName,
	} as AuthRegisterPayload)

	return nextTick(() => {
		loading.value = false
	})
})

const onCloseLogin = () => {
	const query = { ...route.query }
	delete query.auth
	return router.push({
		path: route.path,
		query,
	})
}
</script>

<template>
	<Modal
		size="!p-0"
		:dismissible="false"
		:hide-close="true"
		@close="onCloseLogin"
	>
		<template #body>
			<div class="bg-gray-200 h-52 flex items-center justify-center rounded-t-lg">
				<NuxtImg
					src="/images/logo.png"
					:alt="$t('app.action-mobile-logo')"
					:title="$t('app.action-mobile-logo')"
					class="h-24"
					loading="lazy"
				/>
			</div>
			<div class="grid grid-cols-2 gap-4 w-full overflow-y-auto min-h-[300px] px-4 py-6">
				<FormField
					v-slot="{ componentField }"
					name="firstName"
				>
					<FormItem>
						<FormLabel class="font-semibold">
							{{ $t('form.first-name') }}*
						</FormLabel>
						<FormControl>
							<Input
								v-model="Form.values.firstName"
								type="text"
								:placeholder="$t('form.first-name') "
								v-bind="componentField"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				</FormField>

				<FormField
					v-slot="{ componentField }"
					name="lastName"
				>
					<FormItem>
						<FormLabel class="font-semibold">
							{{ $t('form.last-name') }}*
						</FormLabel>
						<FormControl>
							<Input
								v-model="Form.values.lastName"
								type="text"
								:placeholder="$t('form.last-name') "
								v-bind="componentField"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				</FormField>

				<FormPhone
					:error="Form.errors?.value?.phone"
					@update="(value: FormPhoneValue) => {
						Form.setFieldValue('phone', {
							number: value.nationalNumber,
							code: value.countryCallingCode,
							iso: value.countryCode,
							isValid: value.isValid,
						})
					}"
				/>

				<div class="col-span-2 w-full">
					<FormField
						v-slot="{ componentField }"
						name="password"
					>
						<FormItem class="relative">
							<FormLabel class="font-semibold">
								{{ $t('form.password') }}*
							</FormLabel>
							<FormControl>
								<Input
									:type="!passwordEye?'password':'text'"
									:placeholder="$t('form.password')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
							<div class="flex absolute top-7 end-4">
								<button
									class="p-1"
									@click="() => passwordEye = !passwordEye"
								>
									<Icon
										:name="passwordEye?'lucide:eye':'lucide:eye-off'"
										size="18px"
									/>
								</button>
							</div>
						</FormItem>
					</FormField>
				</div>

				<div class="col-span-2 w-full">
					<FormField
						v-slot="{ componentField }"
						name="confirmPassword"
					>
						<FormItem class="w-full relative ">
							<FormLabel class="font-semibold">
								{{ $t('form.confirm-password') }}*
							</FormLabel>
							<FormControl>
								<Input
									v-model="Form.values.confirmPassword"
									:type="!confirmEye?'password':'text'"
									:placeholder="$t('form.new-password')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
							<div class="flex absolute top-7 end-4">
								<button
									class="p-1"
									@click="() => confirmEye = !confirmEye"
								>
									<Icon
										:name="confirmEye?'lucide:eye':'lucide:eye-off'"
										size="18px"
									/>
								</button>
							</div>
						</FormItem>
					</FormField>
				</div>
			</div>
		</template>
		<template #footer>
			<div class="flex w-full flex-col px-4 gap-4 col-span-2 p-4">
				<div class="flex justify-end px-4 col-span-2">
					<NuxtLink
						:to="`${route.path}?auth=forgot-password`"
						class="hover:underline hover:text-primary-500 text-base text-gray-500"
					>
						{{ $t("auth.forget-password-question") }}
					</NuxtLink>
				</div>
				<Button
					class="w-full"
					@click.prevent="() => doSignUp()"
				>
					{{ $t('auth.sign-up') }}
				</Button>

				<div class="flex flex-col py-2 justify-center items-center relative w-full ">
					<span class="bg-white px-2 z-10 inline text-xs text-gray-500">
						{{ $t('auth.new-gust-hint') }}
					</span>
					<div class="flex w-full bg-gray-200 absolute my-4 left-0 h-px z-0" />
				</div>

				<Button
					variant="outline"
					class="w-full"
				>
					<NuxtLink :to="`${route.path}?auth=login`">
						{{ $t('auth.log-in') }}
					</NuxtLink>
				</Button>
			</div>
		</template>
	</Modal>
</template>
