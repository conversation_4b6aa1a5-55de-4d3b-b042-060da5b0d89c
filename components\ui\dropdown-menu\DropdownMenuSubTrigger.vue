<script setup lang="ts">
import {
	DropdownMenuSubTrigger,
	type DropdownMenuSubTriggerProps,
	useForwardProps,
} from 'reka-ui'
import { computed, type HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<DropdownMenuSubTriggerProps & { class?: HTMLAttributes['class'], hideArrow?: boolean }>()

const delegatedProps = computed(() => {
	const { class: _, hideArrow = false, ...delegated } = props

	return delegated
})

const forwardedProps = useForwardProps(delegatedProps)
const { locale } = useI18n()
const isRtl = computed(() => locale.value === 'ar')
</script>

<template>
	<DropdownMenuSubTrigger
		v-bind="forwardedProps"
		:class="cn(
			'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',
			props.class,
		)"
	>
		<slot />
		<Icon
			v-if="!props.hideArrow"
			name="lucide:chevron-right"
			class="ml-auto h-4 w-4"
			:class="{ 'rotate-180': isRtl }"
		/>
	</DropdownMenuSubTrigger>
</template>
