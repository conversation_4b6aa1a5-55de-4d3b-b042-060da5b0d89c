<script setup lang="ts">
import { useDebounceFn, onClickOutside } from '@vueuse/core'
import { useNuxtApp } from 'nuxt/app'
import { computed } from 'vue'
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
	DropdownMenuItem,
} from '~/components/ui/dropdown-menu'

import { Skeleton } from '~/components/ui/skeleton'
import type { Details } from '~/interfaces/product/details'
import { useCategoriesStore } from '~/store/categoriesStore'

const { locale } = useI18n()
const categoryStore = useCategoriesStore()
const isFetching = computed<boolean>(() => categoryStore?.fetching)
/** All categories */
const categories = computed(() => categoryStore?.search)
/** Watch route to get selected category */
const selected = computed(() => categories?.value?.find(({ id }) => `${id}` === route.query.category))
const route = useRoute()
const keywords = ref(route.query?.q)
const isLoading = ref<boolean>(false)
const products = ref<Details[]>([])
const isListOpen = ref(false)
const triggerRef = useTemplateRef('triggerRef')
const searchRef = ref(null)
const searchInputRef = ref(null)
const dir = computed(() => locale.value === 'ar' ? 'rtl' : 'ltr')
const isRtl = computed(() => dir.value === 'rtl')

/** Update search result */
const debouncedFn = useDebounceFn(() => fetchSearch(), 1000)

/**
 * Handle on a Fetching search result
 * @returns {Promise<void>}
 */
const fetchSearch = async (): Promise<void> => {
	if (!(keywords.value as string)?.trim()) {
		return
	}

	isLoading.value = true
	const { $api } = useNuxtApp()
	return $api<Details[]>(`autocomplete?q=${keywords.value}&perPage=12`)
		.then((data) => {
			products.value = data ?? []
		})
		.catch((error) => {
			console.error(`ERROR Search, ${error}`)
		})
		.finally(() => {
			isLoading.value = false
			isListOpen.value = !!products.value?.length
		})
}

/**
 * On Typing on the input
 */
const onTyping = () => {
	isListOpen.value = !!products.value?.length
	products.value = []
	isLoading.value = true
	debouncedFn()
}

/** handle on search **/
const onSearchClicked = () => {
	isListOpen.value = !!products.value?.length
}

/**
 * Make sure the page not scrolling on case the menu list open
 */
watch(() => isListOpen.value, (value) => {
	if (import.meta.client) {
		document.body.style.overflowY = value ? 'hidden' : 'auto'
	}
}, { immediate: true })

/**
 * Handle on click outside of list
 */
onClickOutside(searchRef, () => {
	isListOpen.value = false
})

/**
 * On click the search result
 * Set the search field value by the selected products name
 */
const onSelectSearch = (product: Details) => {
	keywords.value = product?.name ?? '' as string
	const localePath = useLocalePath()
	const router = useRouter()
	isListOpen.value = false
	nextTick(() => {
		document.body.style.overflowY = 'auto'
		router.push(localePath(`/product/${product.slug}`))
	})
}

onUnmounted(() => {
	isListOpen.value = false
})

const searchInputFocus = () => {
	if (searchInputRef.value) {
		// @ts-ignore
		searchInputRef.value.focus()
	}

	isListOpen.value = !!products.value?.length
}
</script>

<template>
	<div class="max-sm:hidden flex flex-grow max-w-4xl">
		<div class="grid grid-cols-[auto_1fr_auto] w-full rounded-lg  border border-gray-300">
			<div class="flex max-w-40 bg-gray-100 items-center justify-center border-e border-gray-300 rounded-s-md">
				<DropdownMenu>
					<DropdownMenuTrigger
						ref="triggerRef"
					>
						<div class="flex items-center justify-center gap-2 px-4 text-sm text-nowrap min-w-40">
							<span>{{ selected?.name || $t("header.search-all-categories") }}</span>
							<Icon
								name="lucide:chevron-down"
								class="w-4"
							/>
						</div>
					</DropdownMenuTrigger>
					<DropdownMenuContent
						class="w-52 mt-4"
					>
						<div
							class="flex flex-col gap-1 text-sm text-gray-500 cursor-pointer"
						>
							<Skeleton
								v-if="isFetching"
								class="w-full h-6"
							/>

							<NuxtLink
								v-for="category in categories"
								v-else
								:key="category.name"
								:to="`${route.path ?? ''}?category=${category.id}${route.query.q?`&q=${route.query.q}`:''}`"
								class="hover:bg-gray-100 p-2 active:transition-transform"
								:class="{ 'bg-gray-100': !!selected?.id && category?.id === selected?.id }"
							>
								<DropdownMenuItem
									:dir="dir"
									class="cursor-pointer"
									@close-auto-focus="true"
								>
									{{ category.name }}
								</DropdownMenuItem>
							</NuxtLink>
						</div>
					</DropdownMenuContent>
				</DropdownMenu>
			</div>
			<div
				ref="searchRef"
				class="flex items-center flex-col"
			>
				<input
					ref="searchInputRef"
					v-model="keywords"
					name="search"
					type="text"
					autocomplete="off"
					class="w-full h-full outline-none p-2 "
					:placeholder="$t('header.search-placeholder')"
					@input="onTyping"
					@click="onSearchClicked"
				>
				<div
					v-if="isListOpen"
					class="relative w-full z-20"
				>
					<div
						class="flex w-full flex-col bg-white shadow-lg rounded-b-lg absolute left-0 top-px border border-gray-200 border-t-0 max-h-80 overflow-auto"
					>
						<template v-if="isLoading">
							<div
								v-for="(_) in Array(5)"
								:key="`search-loading-${_}`"
								:class="{ 'bg-gray-50': _ % 2 === 0 }"
								class="grid grid-cols-[auto_1fr_auto] w-full gap-2 my-2 px-4 hover:bg-primary-100"
							>
								<Skeleton class="w-10 h-12" />
								<Skeleton class="w-full h-12" />
								<div class="flex flex-col justify-center items-center">
									<Skeleton class="w-6 h-4" />
								</div>
							</div>
						</template>

						<template v-if="!isLoading && !products?.length">
							<div class="flex w-full px-4 py-6">
								<span>{{ $t('header.search-no-result') }}</span>
							</div>
						</template>
						<template
							v-for="(product, index) in products"
							:key="`search-${product.productId}`"
						>
							<div
								role="button"
								:class="{ 'bg-gray-50': index % 2===0 }"
								class="grid grid-cols-[auto_1fr_auto] w-full items-center gap-2 px-4 hover:bg-primary-100"
								@click="onSelectSearch(product)"
							>
								<div class="flex p-2 rounded-lg col-span-1">
									<NuxtImg
										:src="product.media?.cover?.[0]?.src"
										class="object-cover w-12 h-12"
										provider="backend"
										format="webp"
									/>
								</div>
								<span class="col-span-1 text-sm">{{ product.name }}</span>
								<div class="col-span-1 flex flex-col justify-center items-center">
									<Icon
										name="lucide:chevron-right"
										size="18px"
										:class="{ 'rotate-180': isRtl }"
									/>
								</div>
							</div>
						</template>
					</div>
				</div>
			</div>
			<div class="flex items-center justify-center px-4 border-s border-gray-300 bg-white rounded-e-lg">
				<button @click="searchInputFocus">
					<Icon
						name="lucide:search"
						class="text-gray-300"
					/>
				</button>
			</div>
		</div>
	</div>
</template>
