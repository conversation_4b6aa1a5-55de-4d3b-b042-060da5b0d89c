<script setup lang="ts">
import HyperpayForm from '~/components/hyperpay-form.vue'
import Modal from '~/components/modal.vue'
import type { PaymentMethodResponse } from '~/interfaces/auth/payment-method'
import type { CheckoutOrder } from '~/interfaces/checkout/order'
import { useAuthStore } from '~/store/useAuthStore.client'
import { useCartStore } from '~/store/useCartStore'

const { priceFormat } = useCurrency()

const { order } = defineProps<{
	order?: CheckoutOrder
}>()

const emit = defineEmits<{
	(event: 'set:flow', value: number): void
}>()

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const localePath = useLocalePath()
const isSuccessModal = ref(false)
const paymentMethod = computed(() => order?.paymentMethod)
const address = computed(() => order?.address)
const shipment = computed(() => order?.shippingCarrier)

const isHyperPayModal = ref(false)
const isUserInfoModal = ref(false)
const isSubmitting = ref(false)

const isHyperPay = computed(() => order.paymentMethod?.module === 'hyperpay')
const trackingLink = computed(() => localePath(`/my/orders/${order?.orderId}/tracking`))
const isLocked = computed(() => order?.status !== 'draft')
const isUserLoggedIn = computed(() => !!authStore.isLoggedIn)

const userData = ref(null)
const hyperPayResponse = ref(null)

/** on close success modal **/
const onCloseModal = () => {
	if (!isUserLoggedIn.value) {
		return
	}

	router.push(trackingLink.value)
}
const setOnSuccess = () => {
	nextTick(async () => {
		isSuccessModal.value = true
		const cartStore = useCartStore()
		await cartStore.fetchMounted()
	})
}

const payUsingHyperPay = async () => {
	const { $api } = useNuxtApp()
	return $api<unknown>(`/orders/${order.orderId}/set-payment-params`, {
		method: 'POST',
		body: {
			email: userData.value?.email,
			firstName: userData.value?.firstName,
			lastName: userData.value?.lastName,
		},
	}).then((data) => {
		hyperPayResponse.value = data as PaymentMethodResponse
	})
}

const finalizeOrder = () => {
	isSubmitting.value = true
	const { $api } = useNuxtApp()
	$api<unknown>(`/orders/${order.orderId}/finalize`, {
		method: 'POST',
	})
		.then(() => setOnSuccess())
		.finally(() => {
			isSubmitting.value = false
		})
}

const onSubmit = async () => {
	if (isHyperPay.value) {
		// show user information modal
		isUserInfoModal.value = true
		return userData.value = {
			email: order?.address?.email,
			firstName: order?.address?.firstName,
			lastName: order?.address?.lastName,
		}
	}

	return finalizeOrder()
}

const onUpdateUserInfo = async (data) => {
	isSubmitting.value = true
	console.log('onUpdateUserInfo', data)
	userData.value = data
	await payUsingHyperPay()
	return nextTick(() => {
		isHyperPayModal.value = true
		isSubmitting.value = false
	})
}

onMounted(() => {
	if (route?.query?.id) {
		setOnSuccess()
	}
})
</script>

<template>
	<CardContent class="flex-grow p-4">
		<div class="flex flex-col w-full gap-6">
			<div class="flex flex-col gap-2">
				<div class="flex justify-between items-center">
					<span class="font-bold">{{ $t('checkout.contact-title') }}</span>
					<Button
						v-if="!isLocked"
						variant="text"
						@click="() => emit('set:flow', 1)"
					>
						{{ $t('form.edit') }}
					</Button>
				</div>

				<div
					class="flex border rounded-lg gap-4 items-center p-4 md:max-w-sm sm:w-full"
				>
					<div class="flex flex-col flex-grow gap-2 text-sm">
						<div class="font-bold text-base">
							{{ $t(`form.${address?.buildingType}`) }}
						</div>
						<div class="flex w-full items-center gap-4">
							<span class="w-24 text-gray-600">
								{{ $t('form.receiver-name') }}:
							</span>
							<span class="text-gray-700">
								{{ address?.recipientName }}
							</span>
						</div>
						<div class="flex w-full items-center gap-4">
							<span class="w-24 text-gray-600">
								{{ $t('form.address') }}
							</span>
							<span class="text-gray-700 max-w-56">
								{{
									[
										address?.fullAddress,
										address?.district,
										address?.street,
									]
										.filter(Boolean)
										.join(', ')
								}}
							</span>
						</div>

						<div class="flex w-full items-center gap-4">
							<span class="w-24 text-gray-600">
								{{ $t('form.phone') }}
							</span>
							<span
								class="text-gray-700"
								dir="ltr"
							>
								{{
									[
										'+',
										address?.phone.code + ' ',
										address?.phone.number,
									].join('')
								}}
							</span>
						</div>
					</div>
				</div>
			</div>
			<div class="flex w-full border-dashed border border-gray-100 mt-4" />

			<div class="flex flex-col gap-2">
				<div class="flex justify-between items-center">
					<span class="font-bold">{{ $t('checkout.shipping-title') }}</span>
					<Button
						v-if="!isLocked"
						variant="text"
						@click="() => emit('set:flow', 2)"
					>
						{{ $t('form.edit') }}
					</Button>
				</div>

				<div
					class="active flex flex-col border rounded-lg gap-2 md:max-w-sm sm:w-full"
				>
					<div class="flex gap-2 border-b p-2">
						<div class="check flex rounded-full w-5 h-5 border p-0.5">
							<div class="child flex w-full h-full rounded-full" />
						</div>

						<div class="font-bold text-base flex-grow">
							{{ shipment?.label }}
						</div>

						<div class="font-bold">
							{{ priceFormat(order?.shippingPrice?.value) }}
						</div>
					</div>
					<div class="flex w-full items-center text-sm gap-4 px-4 pb-2">
						<span class="w-18 text-gray-600">
							{{ $t('form.the-delivery') }}:
						</span>
						<span class="text-gray-700">
							N/A
						</span>
					</div>
				</div>
			</div>
			<div class="flex w-full border-dashed border border-gray-100 mt-4" />

			<div class="flex flex-col gap-2">
				<div class="flex justify-between items-center">
					<span class="font-bold">{{ $t('checkout.pay-title') }}</span>
					<Button
						v-if="!isLocked"
						variant="text"
						@click="() => emit('set:flow', 3)"
					>
						{{ $t('form.edit') }}
					</Button>
				</div>

				<div class="active flex flex-col border rounded-lg gap-2 md:max-w-sm sm:w-full">
					<div class="flex gap-2 border-b py-2 px-3 items-center">
						<div class="check flex rounded-full w-5 h-5 border p-0.5">
							<div class="child flex w-full h-full rounded-full" />
						</div>

						<div class="font-bold text-base flex-grow">
							{{ paymentMethod?.name }}
						</div>

						<div class="flex w-20 h-5 justify-end">
							<NuxtImg
								provider="backend"
								:src="paymentMethod?.media?.logo?.preview"
							/>
						</div>
					</div>
					<div class="flex w-full items-center text-sm gap-4 px-4 pb-2">
						<span class="font-semibold text-gray-700">
							{{ $t(`checkout.payment-method-${paymentMethod?.module}`) }}:
						</span>
					</div>
				</div>
			</div>
		</div>
	</CardContent>
	<CardFooter
		class="gap-4 justify-end max-sm:justify-normal mt-6"
	>
		<template v-if="!isLocked">
			<Button
				variant="outline"
				class="sm:w-32 xs:w-1/2"
				@click.prevent="() => emit('set:flow', 3)"
			>
				{{ $t("form.prev") }}
			</Button>

			<Button
				class="sm:w-32 xs:w-1/2"
				:loading="isSubmitting"
				@click.prevent="onSubmit"
			>
				{{ $t("checkout.set-order") }}
			</Button>
		</template>
	</CardFooter>

	<Modal
		v-if="isSuccessModal"
		:dismissible="false"
		@close="onCloseModal"
	>
		<template #body>
			<div class="flex flex-col justify-center items-center w-full gap-4 p-4">
				<NuxtImg
					src="/images/success.gif"
					class="w-44"
					loading="eager"
					width="176"
					height="176"
				/>

				<div class="text-base font-bold w-sm px-2 text-center">
					{{ $t('checkout.success-message-text') }}
				</div>
			</div>
		</template>
		<template
			v-if="isUserLoggedIn"
			#footer
		>
			<div class="flex w-full px-4">
				<Button
					class="w-full"
					as-child
				>
					<NuxtLink
						:to="trackingLink"
						class="w-full text-center flex justify-center"
					>
						{{ $t('checkout.check-my-order') }}
					</NuxtLink>
				</Button>
			</div>
		</template>
	</Modal>
	<CheckoutFlowUserInfo
		v-if="isUserInfoModal"
		:user="userData"
		@close="isUserInfoModal = false"
		@update="onUpdateUserInfo"
	/>

	<Modal
		v-if="isHyperPayModal"
		:dismissible="false"
		@close="isHyperPayModal = false"
	>
		<template #body>
			<div class="flex px-4 pt-8">
				<HyperpayForm
					:params="hyperPayResponse"
					:call-back-url="`checkout/${order.orderId}/4`"
				/>
			</div>
		</template>
		<template #footer>
			<Button
				variant="outline"
				class="sm:min-w-24 xs:min-w-full"
				@click="isHyperPayModal = false"
			>
				<span>{{ $t('form.cancel') }}</span>
			</Button>
		</template>
	</Modal>
</template>

<style scoped lang="scss">
.active {
  @apply bg-primary-300/30 border-primary-500;
  .check {
    @apply border-primary-600;
    .child {
      @apply bg-primary-600
    }
  }
}
</style>
