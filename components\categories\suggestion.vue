<script setup lang="ts">
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '~/components/ui/carousel'
import { Skeleton } from '~/components/ui/skeleton'
import type { CategoryDetails } from '~/interfaces/category/details'

interface Props {
	categories?: CategoryDetails[] | undefined
	loading?: boolean
	slug: string
}

const { categories = [], loading, slug } = defineProps<Props>()

/** Convert slug string to array */
const slugs = computed(() => {
	return (slug as string)?.split(',')?.join('/')
})
</script>

<template>
	<div class="w-full group mt-2">
		<Carousel
			v-if="categories?.length"
			:opts="{ align: 'center', slidesToScroll: 'auto' }"
			class="w-full bg-white gap-x-4"
		>
			<template #default="{ canScrollNext, canScrollPrev }">
				<CarouselContent class="max-w-full w-fit m-0">
					<template v-if="loading">
						<div
							v-for="index in Array(4)"
							:key="`card-${index}`"
							class="rounded-lg border border-gray-200 basis-auto me-4"
						>
							<div class="flex items-center gap-4 w-full h-full p-2">
								<Skeleton class="w-16 h-16" />
								<Skeleton class="w-16 h-5" />
							</div>
						</div>
					</template>
					<CarouselItem
						v-for="category in categories"
						v-else
						:key="category.name"
						:data-slug="category.slug"
						class="rounded-lg border border-gray-200 basis-auto me-4"
					>
						<NuxtLinkLocale
							:title="category.name"
							:to="`/category/${slugs}/${category.slug}`"
						>
							<div class="flex items-center gap-4 w-full h-full p-2">
								<div class="flex w-16 max-h-16  items-center justify-center p-1">
									<NuxtImg
										:lazy="true"
										:src="category.media?.cover?.src"
										:alt="category.name"
										:title="category.name"
										object-fit="cover"
										object-position="center"
										class="h-auto"
										height="56"
										width="56"
										format="webp"
										loading="eager"
										provider="backend"
									/>
								</div>
								<span class="text-md font-semibold text-gray-500 text-nowrap">
									{{ category.name }}
								</span>
							</div>
						</NuxtLinkLocale>
					</CarouselItem>
				</CarouselContent>
				<CarouselPrevious v-if="canScrollPrev" />
				<CarouselNext v-if="canScrollNext" />
			</template>
		</Carousel>
	</div>
</template>

<style scoped lang="scss">

</style>
