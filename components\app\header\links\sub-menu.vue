<script setup lang="ts">
import type { LockupCategories } from '~/interfaces/lockup/categories'

const { item, path, close } = defineProps<{
	item: LockupCategories
	path?: string
	close: () => void
}>()

const { locale } = useI18n()
const isLtr = computed<boolean>(() => locale.value === 'en')
const isMenu = ref<boolean>(false)
</script>

<template>
	<DropdownMenuGroup
		v-model:open="isMenu"
	>
		<template v-if="!item?.meta?.children?.length">
			<!-- Normal Item (Team users) -->
			<DropdownMenuItem
				class="!py-0 min-w-52"
			>
				<div
					class="cursor-default select-none rounded-sm outline-none focus:bg-accent data-[state=open]:bg-accent flex justify-between items-start text-sm font-normal text-gray-500 py-2 min-w-52"
				>
					<NuxtLink
						:to="`${path}/${item?.meta?.slug}`"
						class="flex w-full justify-between py-1"
					>
						<span>{{ item.text }} </span>
					</NuxtLink>
				</div>
			</DropdownMenuItem>
		</template>

		<template v-else>
			<!-- Menu Item (Invite users) -->
			<DropdownMenuSub class="flex w-full">
				<DropdownMenuSubTrigger
					class="flex justify-between items-start text-sm font-normal text-gray-500 px-2 py-3 min-w-52"
					:hide-arrow="true"
					as-child
				>
					<NuxtLink
						:to="`${path}/${item.meta?.slug}`"
						@click="() => close()"
					>
						<span>{{ item.text }}  </span>
						<span v-if="!isLtr" />
						<Icon
							name="lucide:chevron-right"
							class="ms-auto h-4 w-4"
							:class="{ 'rotate-180': !isLtr }"
						/>
					</NuxtLink>
				</DropdownMenuSubTrigger>
				<!-- Sub Menu List (Email, Message, More) -->
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<template
							v-for="(link, index) in item.meta?.children"
							:key="`${index}-${JSON.stringify(item.text)}`"
						>
							<DropdownMenuItem
								v-if="!link?.meta?.children?.length"
								class="cursor-pointer text-sm font-normal text-gray-500 px-2 py-3 min-w-52"
							>
								<NuxtLink
									:to="`${path}/${item.meta?.slug}/${link.meta?.slug}`"
									class="flex w-full justify-between "
								>
									<span>{{ link.text }} </span>
								</NuxtLink>
							</DropdownMenuItem>

							<template v-else>
								<AppHeaderLinksSubMenu
									:item="link"
									:close="() => close()"
									:path="`${path}/${item.meta?.slug}`"
								/>
							</template>
						</template>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</dropdownmenusub>
		</template>
	</DropdownMenuGroup>
</template>
