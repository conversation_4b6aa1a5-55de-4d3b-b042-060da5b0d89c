export interface Details {
	description?: string
	media?: ProductMedia
	type?: string
	metaDescription?: string
	paymentMethods?: PaymentMethod[]
	categories?: Category[]
	brand?: Brand
	slug?: string
	productId?: number
	isPublished?: boolean
	isListed?: boolean
	variance?: Variance
	brandId?: number
	metaTitle?: string
	name?: string
	avgRate?: number
	variances?: VarianceElement[]
	variationAttributes?: VariationAttribute[]
	attributes?: ProductAttribute[]
	SKU?: string

	code?: number
	message?: string
	details?: RedirectResponseDetails
}

export interface RedirectResponseDetails {
	redirectUrl: string
}

export interface RedirectResponse {
	code: number
	message: string
	details: RedirectResponseDetails
}

export type UseProductDetails = Details & {
	cartId?: number
	[key: string]: unknown | Ref<unknown | string> | ComputedRef<unknown | string> | string | number | boolean | null | undefined
}

export interface ProductAttribute {
	attributeId: number
	isRequired: boolean
	prefix: string
	name: string
	hasFilter: boolean
	suffix: string
	type: string
	value: ValueElement
	key: string
	slug: string
	icon?: string
}

export interface ValueElement {
	number?: string
	name: string
	attributeOptionId?: number
	attributeOptionsStock?: AttributeOptionsStock
	slug?: string
	value?: string
	hexCode?: string
	media: ProductMedia
	stock?: Stock
}

export interface Brand {
	brandId: number
	metaTitle: string
	name: string
	media: BrandMedia
	metaDescription: string
	slug: string
}

export interface BrandMedia {
	logoName: Cover[]
	logo: Cover[]
}

export interface Cover {
	preview: string
	disk: Disk
	src: string
	fileSize: number
	id: number
	mimeType: MIMEType
	sort: number
}

export enum Disk {
	S3 = 's3',
}

export enum MIMEType {
	ImagePNG = 'image/png',
	ImageWebp = 'image/webp',
}

export interface Category {
	metaTitle: string
	name: string
	metaDescription: string
	isShowBrandIngListing: boolean
	categoryId: number
	slug: string
	parentId?: number
}

export interface ProductMedia {
	cover: Cover[]
	gallery: Cover[]
}

export interface PaymentMethod {
	createdAt: Date
	allowFillWallet: number
	paymentMethodId: number
	module: string
	name: string
	media: PaymentMethodMedia
	updatedAt: Date
	status: string
}

export interface PaymentMethodMedia {
	logo: Cover
}

export interface Variance {
	varianceId: number
	brandId: number
	metaTitle: string
	name: string
	attributes: VarianceAttribute[]
	media: ProductMedia
	type: Type
	stock: Stock
	metaDescription: string
	slug: string
	SKU?: string
}

export interface VarianceAttribute {
	attributeId: number
	isRequired: boolean
	prefix: string
	name: string
	hasFilter: boolean
	suffix: string
	type: string
	value: Value
	key: string
	slug: string
}

export interface Value {
	hexCode?: string
	extra: Extra
	name: string
	attributeOptionId: number
	slug: string
	number?: string
}

export interface Extra {
}

export interface Stock {
	note: string
	quantity: number
	supplierId: number
	isOffer: boolean
	price: Price
	stockId: number
	priceBeforeOffer: Price
	isPreOrder: boolean
	unPublishedAt: Date
	maxPerUser: number
}

export interface Price {
	symbol: string
	currency: string
	currencyId: number
	value: number
}

export enum Type {
	Physical = 'physical',
}

export interface VarianceElement {
	varianceId: number
	brandId: number
	metaTitle: string
	name: string
	type: Type
	metaDescription: string
	slug: string
	attributeOptionsId?: number[]
	stock?: Stock
}

export interface VariationAttribute {
	attributeId: number
	prefix: string
	name: string
	options: ValueElement[]
	suffix: string
	type: string
	icon?: string
	value: VariationAttributeValue
}

export interface VariationAttributeValue {
	extra: Extra
	name: string
	attributeOptionId: number
	slug: string
	variance: Variance
}

export interface AttributeOptionsStock {
	attributeOptionId?: number[]
	stock?: AttributeStock
}
export interface AttributeStock {
	stockId?: number
	quantity?: number
	maxPerUser?: number
	publishedAt?: string
	unPublishedAt?: string
	sort?: number
	isPublished?: boolean
	supplierId?: number
	price?: number
	cost?: number
	priceBeforeOffer?: number
	isOffer?: boolean
	sold?: number
	createdAt?: string
	updatedAt?: string
	isPreOrder?: boolean
	note?: string
	laravel_through_key?: number
}
