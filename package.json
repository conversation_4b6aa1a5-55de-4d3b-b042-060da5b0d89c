{"name": "action-mobile", "private": true, "type": "module", "scripts": {"build": "nuxt build", "build:lambda": "npx nuxt build --envName lambda", "dev": "nuxt dev", "generate": "nuxt generate", "deploy": "npx serverless deploy", "capacitor": "nuxt generate --dotenv .env.capacitor --envName capacitor", "preview": "nuxt preview", "postinstall": "nuxt prepare", "type-check": "vue-tsc --project tsconfig.json --noEmit --skipLib<PERSON><PERSON>ck", "lint": "eslint --ext .ts,.vue .", "lint:fix": "eslint --ext .ts,.vue --fix .", "clean:run": "rm -rf .nuxt && rm -rf node_modules/.cache && npm run dev", "cap:sync": "npx cap sync", "cap:build": "npm run generate:capacitor && npm run cap:sync", "cap:android": "npm run cap:build && npx cap run android", "cap:ios": "npm run cap:build && npx cap run ios"}, "dependencies": {"@capacitor/android": "7.2.0", "@capacitor/app": "7.0.1", "@capacitor/core": "7.2.0", "@capacitor/haptics": "7.0.1", "@capacitor/ios": "7.2.0", "@capacitor/keyboard": "7.0.1", "@capacitor/status-bar": "7.0.1", "@nuxt/eslint": "^1.1.0", "@nuxt/fonts": "^0.11.0", "@nuxt/icon": "^1.11.0", "@nuxt/image": "^1.10.0", "@nuxt/scripts": "^0.11.6", "@pinia/nuxt": "^0.10.1", "@sentry/nuxt": "^9.19.0", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/vue-table": "^8.21.2", "@unhead/vue": "^2.0.8", "@vee-validate/zod": "^4.15.0", "base-vue-phone-input": "^0.1.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-vue": "^8.5.2", "nuxt": "^3.16.2", "nuxt-multi-cache": "^3.4.0", "pinia": "^3.0.2", "reka-ui": "^2.2.0", "serverless-offline": "^14.4.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul-vue": "^0.4.1", "vee-validate": "^4.15.0", "vue": "latest", "vue-router": "latest", "vue-sonner": "^1.3.0", "zod": "^3.24.2"}, "devDependencies": {"@capacitor/cli": "7.2.0", "@iconify-json/lucide": "^1.2.44", "@nuxt/eslint-config": "^1.1.0", "@nuxt/types": "^2.18.1", "@nuxtjs/i18n": "^9.0.0-rc.2", "@nuxtjs/tailwindcss": "^6.13.1", "@types/node": "^22.13.5", "@types/vue": "^2.0.0", "@vueuse/core": "^12.8.2", "@vueuse/nuxt": "^12.8.2", "eslint": "^9.21.0", "eslint-typegen": "^2.0.0", "sass-embedded": "^1.85.0", "serverless": "^4.14.3", "shadcn-nuxt": "^1.0.1", "typescript": "^5.7.3", "vue-tsc": "^2.1.10"}}