import type { Details } from '~/interfaces/product/details'

export interface ProductList {
	pagination?: Pagination
	filters?: Filters
	items?: Details[]
}

export interface Filters {
	search: OrderBy
	isPublished: ID
	order_by: OrderBy
	id: ID
	category: Category
	priceRange: ID
	brand: Brand
}

export interface Brand {
	name: string
	options: unknown[]
	type: string
	value: unknown[]
}

export interface Category {
	options: Option[]
	type: string
}

export interface Option {
	text: string
	value: number
}

export interface ID {
	type: string
}

export interface OrderBy {
}

export type Item = Partial<Details> & {
	hasStock: boolean
	productId: number
	description: string
	media: Media
	type: Type
	colors: Color[]
	isListed: boolean
	variance: Variance
	brandId: number
	minPrice: MaxPrice
	name: string
	avgRate: number
	maxPrice: MaxPrice
	SKU: string
	brand: string
	slug: string
	hasOffer: boolean
	minPriceBeforeOffer?: MaxPrice
	maxPriceBeforeOffer?: MaxPrice
}

export interface Color {
	hexCode: string
	extra?: OrderBy
	name: Name
	attributeOptionId: number
}

export interface Name {
	ar: string
	en: string
}

export interface MaxPrice {
	symbol: Currency
	currency: Currency
	currencyId: number
	value: number
}

export enum Currency {
	Jod = 'JOD',
}

export interface Media {
	cover: Cover[]
	gallery: Cover[]
}

export interface Cover {
	preview: string
	disk: Disk
	src: string
	fileSize: number
	id: number
	mimeType: MIMEType
	sort: number
}

export enum Disk {
	S3 = 's3',
}

export enum MIMEType {
	ImageJPEG = 'image/jpeg',
	ImagePNG = 'image/png',
	ImageWebp = 'image/webp',
}

export enum Type {
	Alternative = 'alternative',
	Simple = 'simple',
}

export interface Variance {
	varianceId: number
	brandId: number
	name: string
	media: Media
	type: Type
	stock: Stock
	slug: string
	SKU?: string
}

export interface Stock {
	note: string
	quantity: number
	supplierId: number
	isOffer: boolean
	price: MaxPrice
	stockId: number
	priceBeforeOffer: MaxPrice
	isPreOrder: boolean
	maxPerUser: number
	unPublishedAt?: Date
}

export interface Pagination {
	total: number
	perPage: number
	lastPage: number
	count: number
	page: number
}
