/**
 * Meta Data composable
 * @param title
 * @param description
 * @param metaTitle
 * @param metaDescription
 * @param media
 * @param fullPath
 * @param pageName
 * @param largeMedia
 * @param link
 */
export const useMetaData = ({
	title = null,
	description = null,
	metaTitle = null,
	metaDescription = null,
	media = '/images/logo.png',
	fullPath = '/',
	pageName = null,
	link = 'https://action.jo',
}) => {
	const { t } = useI18n()

	/** set seo meta **/
	const setMetaData = () => {
		useSeoMeta({
			// Basic meta tags
			title: () => title || t('header.meta-title'),
			description: () => description || t('header.meta-description'),

			// Open Graph meta tags
			ogTitle: () => metaTitle || t('header.meta-title'),
			ogDescription: () => metaDescription || t('header.meta-description'),
			ogImage: () => media,
			ogUrl: () => fullPath,
			ogType: () => pageName,
			ogSiteName: 'Action.jo',

			// Twitter meta tags
			twitterTitle: () => metaTitle || t('header.meta-title'),
			twitterDescription: () => metaDescription || t('header.meta-description'),
			twitterImage: () => media,
		})
	}

	/** set head meta **/
	const setHeadData = () => {
		useHead({
			link: link ? [{ rel: 'canonical', href: `${link}${fullPath || '/'}` }] : [],
			script: [
				{
					type: 'application/ld+json',
					innerHTML: JSON.stringify({
						'@context': 'https://schema.org',
						'@type': 'WebPage',
						'name': metaTitle || title,
						'description': metaDescription || description,
						'url': fullPath,
						'image': media,
					}),
				},
			],
		})
	}

	/**
	 * Build both Head and SEO MetaData
	 */
	const setSeoData = () => {
		setMetaData()
		setHeadData()
	}

	return { setMetaData, setHeadData, setSeoData }
}
