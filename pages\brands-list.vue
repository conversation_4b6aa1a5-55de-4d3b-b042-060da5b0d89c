<script setup lang="ts">
import Paginate from '~/components/ui/pagination/Paginate.vue'
import type { Brand, BrandList, Pagination } from '~/interfaces/brands/brand'

const { t } = useI18n()
const page = ref(1)

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('brands.title'))
})

const { data, error, status } = await useApi<BrandList>('/brands', {
	params: {
		perPage: 24,
		page,
	},
	watch: [page],
})

if (error.value) {
	console.log(`Error on fetching brands: ${error.value}`)
}

const brands = computed(() => (data.value as BrandList).items as Brand[])
const pagination = computed(() => (data.value as BrandList).pagination as Pagination)
const loading = computed(() => status.value !== 'success')
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>
	<Card class="flex flex-col w-full h-full gap-2 my-6">
		<CardHeader class="text-center justify-center gap-4 rounded-lg">
			<h1 class="font-bold text-2xl">
				{{ $t('brands.title') }}
			</h1>
			<h2 class="text-xl">
				{{ $t('brands.sub-title') }}
			</h2>
		</CardHeader>
		<CardContent class="grid grid-cols-5 max-sm:grid-cols-2 gap-6 py-12">
			<template v-if="loading">
				<div
					v-for="(_, index) in Array(25)"
					:key="`loading-brand-${index}`"
					class="flex flex-col p-4 rounded-lg shadow gap-2 border"
				>
					<div class="flex flex-col gap-2 h-full items-center justify-between">
						<div class="flex flex-grow justify-center items-center">
							<Skeleton class="h-16 w-28" />
						</div>
						<Skeleton class="h-4 w-28" />
					</div>
				</div>
			</template>
			<template v-else>
				<div
					v-for="(item, index) in brands"
					:key="index"
					class="flex flex-col p-4 rounded-lg shadow gap-2 border"
				>
					<div class="flex flex-col gap-2 h-full items-center justify-between">
						<div class="flex flex-grow justify-center items-center">
							<NuxtImg
								:src="item?.media?.logoName?.preview"
								:alt="`${item.name} brand logo`"
								object-fit="cover"
								object-position="center"
								height="56"
								width="100"
								format="webp"
								loading="eager"
								provider="backend"
							/>
						</div>
						<span class="text-base font-semibold">{{ item.name }}</span>
					</div>
				</div>
			</template>
		</CardContent>
		<CardFooter v-if="!!brands?.length">
			<div
				class="flex justify-center items-center w-full"
			>
				<Paginate
					:items-per-page="pagination?.perPage"
					:total="pagination?.lastPage"
					:sibling-count="1"
					:show-edges="true"
					:default-page="pagination.page"
					@update:page="(p) => page = p"
				/>
			</div>
		</CardFooter>
	</Card>
</template>
