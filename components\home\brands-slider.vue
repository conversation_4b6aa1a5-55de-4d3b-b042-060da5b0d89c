<script setup lang="ts">
import { useMediaQuery } from '@vueuse/core'
import { ref } from 'vue'
import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselPrevious,
	CarouselNext, type CarouselApi,
} from '@/components/ui/carousel'
import type { LockupBrands } from '~/interfaces/lockup/brands'
import { Skeleton } from '~/components/ui/skeleton'

const { data, error, status } = useApi<LockupBrands>('/lookups-website/brands', {
	query: { inHomePage: 1 },
})

if (error.value) {
	console.error('Error fetching brands slider section:', error.value)
}

const isMobile = useMediaQuery('(max-width: 600px)')
const brands = computed(() => data.value as LockupBrands[])
const isLoading = computed<boolean>(() => status.value !== 'success')

const sliderMainApi = ref<CarouselApi>()
const selectedIndex = ref(0)

const brandsList = computed(() => {
	const result = []
	const items = brands?.value || []

	for (let i = 0; i < items.length; i += 2) {
		result.push(items.slice(i, i + 2))
	}

	return result
})

const mobileDotsLength = computed(() => brandsList.value?.length ? Number(brandsList.value?.length ?? 1) / 2 : 4)

/**
 * On a small image clicked
 * @param index
 */
const onThumbClick = (index: number) => {
	if (!sliderMainApi.value) {
		return
	}

	sliderMainApi.value.scrollTo(index)
	selectedIndex.value = sliderMainApi.value.selectedScrollSnap()
}

/**
 * Watch on the slider
 */
const handleOnChange = (mainApi: CarouselApi) => {
	selectedIndex.value = mainApi.selectedScrollSnap()
}

onMounted(() => {
	if (sliderMainApi.value) {
		sliderMainApi.value.on('select', handleOnChange)
	}
})
</script>

<template>
	<Card class="col-span-3 w-full">
		<div class="px-4 py-4 justify-between items-center flex">
			<span class="text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl">
				{{ $t('home.brands-list') }}
			</span>
		</div>
		<CardContent class="sm:!px-4 xs:!px-2">
			<Carousel
				v-if="!isMobile"
				:opts="{ align: 'center' }"
				class="w-full rounded-lg"
			>
				<template #default="{ canScrollNext, canScrollPrev }">
					<CarouselContent class="min-h-28">
						<template v-if="isLoading">
							<div
								v-for="index in Array(8)"
								:key="`card-${index}`"
								class="basis-1/8 sm:basis-1/5 xl:basis-1/10 me-4"
							>
								<div class="flex flex-col items-center gap-4 w-full h-full">
									<Skeleton class="min-w-32 w-full h-full" />
								</div>
							</div>
						</template>
						<CarouselItem
							v-for="(brand, brandIndex) in brands"
							v-else
							:key="brand.value"
							class="basis-1/8 sm:basis-1/5 xl:basis-1/10 "
							:class="{ '!ps-0': !brandIndex }"
						>
							<NuxtLinkLocale
								:to="`/brands/${brand.meta.slug}`"
								:title="brand.text"
							>
								<div class="flex flex-col h-full items-center justify-center shadow rounded-lg bg-gray-100 min-w-32 p-4">
									<NuxtImg
										:src="brand.meta.media.logoName?.[0]?.preview"
										:alt="brand.text"
										:title="brand.text"
										provider="backend"
										class="max-h-16 h-full"
										width="auto"
										height="63"
										format="webp"
										quality="90"
										fit="contain"
										loading="lazy"
									/>
								</div>
							</NuxtLinkLocale>
						</CarouselItem>
					</CarouselContent>
					<template v-if="!isLoading">
						<CarouselPrevious v-if="canScrollPrev" />
						<CarouselNext v-if="canScrollNext" />
					</template>
				</template>
			</Carousel>

			<Carousel
				v-else-if="isMobile"
				:opts="{ align: 'center', slidesToScroll: 'auto' }"
				class="w-full rounded-lg ms-2"
				@init-api="(val) => sliderMainApi = val"
				@slide="handleOnChange"
			>
				<CarouselContent class="gap-x-3">
					<template v-if="isLoading">
						<CarouselItem
							v-for="index in Array(8)"
							:key="`card-${index}`"
							class="basis-1/10 sm:basis-1/8 md:basis-1/10 lg:basis-1/12 xl:basis-1/14"
						>
							<div class="flex flex-col items-center gap-2 w-full h-full">
								<div class="flex flex-col items-center gap-4 w-full h-full">
									<Skeleton class="w-32 h-full" />
								</div>
								<div class="flex flex-col items-center gap-4 w-full h-full">
									<Skeleton class="w-32 h-full" />
								</div>
							</div>
						</CarouselItem>
					</template>
					<template v-else>
						<CarouselItem
							v-for="(items, index) in brandsList"
							:key="`mobile-category-${index}`"
							class="basis-5/12 !p-0"
							as-child
							:selected="selectedIndex"
						>
							<div class="flex flex-col items-center gap-4 w-full h-full">
								<template
									v-for="(brandItem, cIndex) in items"
									:key="`mobile-category-brand-item-${cIndex}`"
								>
									<NuxtLinkLocale
										:to="`/brands/${brandItem?.slug}`"
										:title="brandItem.text"
										class="flex flex-col items-center justify-between gap-4 w-full h-full max-h-16 bg-gray-100 shadow rounded-lg "
									>
										<div class="flex h-16 p-4 w-full justify-center">
											<NuxtImg
												:src="brandItem.meta.media.logoName?.[0]?.preview"
												:alt="brandItem.text"
												:title="brandItem.text"
												provider="backend"
												class=" w-auto h-full"
												width="106"
												height="32"
												format="webp"
												quality="90"
												fit="contain"
												loading="lazy"
											/>
										</div>
									</NuxtLinkLocale>
								</template>
							</div>
						</CarouselItem>
					</template>
				</CarouselContent>
			</Carousel>
		</CardContent>

		<CardFooter v-if="isMobile">
			<div class="flex w-full gap-2 pt-6 justify-center items-center sm:hidden">
				<div
					v-for="(_, iIndex) in Array(mobileDotsLength)"
					:key="`scroll-thumb-image-${iIndex}`"
					class="flex p-1 rounded-full bg-gray-300 h-1 transition ease-in-out duration-200"
					:data-index="iIndex"
					:class="{ 'bg-primary-600 px-4': iIndex === selectedIndex }"
					@click="onThumbClick(iIndex)"
				/>
			</div>
		</CardFooter>
	</Card>
</template>
