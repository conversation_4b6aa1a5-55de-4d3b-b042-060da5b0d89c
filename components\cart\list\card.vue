<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'
import { computed, onBeforeMount, ref, watch } from 'vue'
import type { Items as Product } from '~/interfaces/cart/cart-list'
import type { QuantityPayload } from '~/interfaces/payload'
import type { UseProductDetails } from '~/interfaces/product/details'

interface Props {
	product: Product
	loading?: boolean
}

const { product, loading } = defineProps<Props>()

const emit = defineEmits<{
	(event: 'update:quantity', data: QuantityPayload): void
	(event: 'remove:cart', cartId: number): void
}>()

// @ts-ignore
const details = useProduct(product as Product) as UseProductDetails

const item = ref<UseProductDetails>(details as UseProductDetails)

// Quantity ref
const quantity = ref<number>(1)

// Compute image src from item (cast to UseProductDetails for access)
const image = computed(() => (item.value as UseProductDetails)?.media?.cover?.[0]?.src ?? '')

/** Mark item as removed and trigger 'remove:cart' event with cart ID. */
const onRemove = () => {
	emit('remove:cart', product.cartId)
}

/** Increment the item quantity if stock allows. */
const onIncrease = () => {
	if (!item.value?.variance) {
		return
	}

	const maxPerUser = item.value.variance.stock?.maxPerUser ?? 0

	if (!maxPerUser) {
		return
	}

	if (quantity.value < maxPerUser) {
		quantity.value += 1
		updateQuantity()
	}
}

/** Decrement the item quantity if greater than 1. */
const onDecrease = () => {
	if (quantity.value > 1) {
		quantity.value -= 1
		updateQuantity()
	}
}

/**
 * Debounced function to update product quantity.
 */
const updateQuantity = useDebounceFn(() => {
	const currentItem = item.value as UseProductDetails
	emit('update:quantity', {
		quantity: quantity.value as number,
		productId: currentItem.productId as number,
		varianceId: currentItem.varianceId as number,
		bundleId: null,
	})
}, 1000)

/** Sync initial quantity with product */
watch(() => product.quantity, (value) => {
	quantity.value = value ?? 1
}, { immediate: true })

/** Convert product into enriched product details before mount */
onBeforeMount(() => {
	// @ts-ignore
	item.value = useProduct(product) as UseProductDetails
	quantity.value = product?.quantity ?? 1
})
</script>

<template>
	<div
		v-if="item?.variance"
		class="flex w-full border border-gray-200 rounded-lg overflow-hidden transition-all ease-in-out duration-300"
	>
		<div
			class="flex gap-1 h-36 w-full"
		>
			<NuxtLinkLocale
				:to="`/product/${item.variance.slug}`"
				class="flex h-full bg-gray-100 flex-col justify-center items-center overflow-hidden box-border p-4 w-36 max-sm:w-28 max-sm:p-1"
			>
				<div class="flex p-2 w-full h-full justify-center items-center">
					<template v-if="loading">
						<Skeleton class="w-32 h-32 max-sm:h-24 max-sm:w-24" />
					</template>
					<template v-else>
						<NuxtImg
							:src="image"
							:alt="item.name"
							provider="backend"
							format="webp"
							class="object-contain h-32 w-32 max-sm:object-cover max-sm:full max-sm:w-full"
						/>
					</template>
				</div>
			</NuxtLinkLocale>
			<div class="flex flex-col justify-evenly px-2 h-full overflow-hidden flex-grow">
				<template v-if="loading">
					<Skeleton class="w-full h-5" />
					<Skeleton class="w-1/3 h-5" />
					<Skeleton class="1/3 h-5" />
				</template>
				<template v-else>
					<div class="flex w-full items-start justify-between">
						<NuxtLinkLocale
							:to="`/product/${item.variance.slug}`"
							class="truncate-2-line text-sm font-semibold"
						>
							{{ item.name }}
						</NuxtLinkLocale>
						<button
							class="bg-gray-100 rounded-lg p-2 flex justify-center items-center hover:bg-gray-200 transition-all duration-300"
							@click.prevent="onRemove"
						>
							<Icon
								name="lucide:trash-2"
								size="20px"
								class="text-gray-400"
							/>
						</button>
					</div>
					<NuxtLinkLocale
						class="flex w-full gap-2"
						:to="`/product/${item.variance.slug}`"
					>
						<span class="text-green-600 text-lg font-bold">{{ item.priceFormatted }}</span>
						<div class="flex justify-between items-center gap-2">
							<div class="flex items-start justify-between gap-4">
								<div class="flex flex-col">
									<div
										v-if="item.discountAmount"
										class="flex gap-2 items-center text-md"
									>
										<span class="text-gray-600 line-through">{{ item.offerPriceFormatted }}</span>
									</div>
								</div>

								<div
									v-if="!!item.discountPercent"
									class="flex"
								>
									<span class="bg-orange-400 text-white text-xs font-semibold px-4 py-1 rounded-full">
										{{ $t('product.card-discount', { amount: item.discountPercent }) }}
									</span>
								</div>
							</div>
						</div>
					</NuxtLinkLocale>
					<div
						class="flex items-center gap-6"
					>
						<div
							id="price-section"
							class="grid grid-cols-[auto_1fr_auto] border border-gray-200 rounded max-w-44 items-center overflow-hidden"
						>
							<button
								class="border-e border-gray-200 w-7 items-center flex justify-center h-7"
								@click.prevent="onIncrease"
							>
								<Icon
									name="lucide:plus"
									height="12px"
									width="12px"
								/>
							</button>
							<input
								v-model="quantity"
								name="quantity"
								type="number"
								readonly
								class="no-spinner border-none outline-none col-span-1 w-auto max-w-10 text-center text-xs"
								:max="2"
							>
							<button
								class="border-s border-gray-200 w-7 h-full items-center flex justify-center"
								@click.prevent="onDecrease"
							>
								<Icon
									name="lucide:minus"
									width="12px"
								/>
							</button>
						</div>
					</div>
				</template>
			</div>
		</div>
	</div>
</template>
