<script setup lang="ts">
import ImagesSlider from './images/slider.vue'
import ImagesFullScreen from './images/fullscreen.vue'
import type { Cover, UseProductDetails } from '~/interfaces/product/details'

const { product, loading } = defineProps<{
	product?: UseProductDetails
	loading?: boolean
}>()

/** Images list **/
const gallery = computed(() => product?.variance?.media?.gallery || product?.media?.gallery as Cover[])

const isFullScreen = ref<boolean>(false)
const name = ref<string>(product?.name)

/**
 * Make sure the page not scrolling on case the fullscreen ON
 */
watch(() => isFullScreen.value, (value) => {
	if (import.meta.client) {
		document.body.style.overflowY = value ? 'hidden' : 'auto'
	}
}, { immediate: true })
</script>

<template>
	<div class="flex gap-4 xs:w-full sm:w-1/2 md:max-w-md w-full">
		<ImagesSlider
			:images="gallery"
			:loading="loading"
			:name="name"
			@set:preview="(value) => isFullScreen = !!value"
		/>

		<ImagesFullScreen
			v-if="isFullScreen"
			style="z-index: 99999999"
			:images="gallery"
			:name="name"
			@set:preview="(value) => isFullScreen = !!value"
		/>
	</div>
</template>
