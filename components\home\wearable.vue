<script setup lang="ts">
import type { Details as ProductDetail } from '~/interfaces/product/details'

interface ProductResponse {
	items?: ProductDetail[]
}

const { data, error, status } = useApi<ProductResponse>('/products', {
	query: {
		perPage: 4,
		categories: 'wearables',
		orderBy: 'numberOfOrder,desc',
	},
})

if (error.value) {
	console.error('Error fetching most wearable section:', error.value)
}

const response = computed<ProductResponse>(() => data.value)
const products = computed<ProductDetail[]>(() => response.value?.items)
const isLoading = computed<boolean>(() => status.value !== 'success')
</script>

<template>
	<Card class="col-span-1 flex flex-col max-md:col-span-3">
		<div class="px-4 py-4 justify-between items-center flex">
			<h2 class="text-lg text-gray-600 font-semibold">
				{{ $t('home.most-popular-wearable') }}
			</h2>

			<NuxtLinkLocale
				to="/category/wearables"
				class="max-sm:text-xs text-primary-600 text-nowrap"
			>
				{{ $t('home.see-more') }}
			</NuxtLinkLocale>
		</div>
		<CardContent class="grid grid-cols-2 grid-rows-2 gap-4 px-4">
			<template v-if="isLoading">
				<div
					v-for="(_, index) in Array(4)"
					:key="`loading-${index}`"
					class="flex rounded-lg  border-gray-200 border bg-gray-100  justify-center flex-col min-h-52 px-2 max-sm:min-h-36"
				>
					<Skeleton class="max-h-52 aspect-video" />
				</div>
			</template>
			<template v-else>
				<NuxtLinkLocale
					v-for="(product, index) in products"
					:key="`product-${index}`"
					:to="`/category/wearables/${product.slug}`"
					:title="product?.name"
					class="flex rounded-lg  border-gray-200 border bg-gray-100  justify-center flex-col min-h-52 px-2 max-sm:min-h-36"
				>
					<NuxtImg
						class="w-full"
						provider="backend"
						:src="product.media?.cover?.[0]?.src"
						width="179"
						height="181"
						format="webp"
						quality="90"
						fit="contain"
						loading="lazy"
						:alt="product?.name"
						:title="product?.name"
					/>
				</NuxtLinkLocale>
			</template>
		</CardContent>
	</Card>
</template>
