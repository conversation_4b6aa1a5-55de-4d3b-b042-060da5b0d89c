<script setup lang="ts">
import { onKeyStroke } from '@vueuse/core'
import SliderImages from './slider.vue'
import type { Cover } from '~/interfaces/product/details'

const { images = [] } = defineProps<{
	images?: Cover[]
	name?: string
}>()

const emit = defineEmits<{
	(event: 'set:preview', value: boolean): void
}>()

/** handle on user click escape to close **/

onKeyStroke('Escape', (event) => {
	event.preventDefault()
	emit('set:preview', false)
})
</script>

<template>
	<div
		class="flex flex-col items-center justify-center fixed !w-screen h-screen bg-black bg-opacity-90 left-0 top-0 z-50 sm:px-6 xs:p-0"
	>
		<SliderImages
			:images="images"
			:name="name"
			:is-full-screen="true"
			@set:preview="(value) => emit('set:preview', value)"
		/>
	</div>
</template>
