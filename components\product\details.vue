<script setup lang="ts">
import PriceCard from '~/components/product/info/price-card.vue'
import type { UseProductDetails } from '~/interfaces/product/details'

interface Props {
	product?: UseProductDetails
	loading?: boolean
}

const { product, loading } = defineProps<Props>()

type SelectVariantType = {
	varianceSlug?: string
	varianceIds?: number[]
}
const emit = defineEmits<{
	(event: 'select:variant', value: SelectVariantType): void
}>()

const route = useRoute()
const { locale } = useI18n()

const { brand, brandLogo, isAvailable, sku } = product

const detailsLink = `${route.path}#details`

const rateLink = `${route.path}?tab=rating&form=rating#details`

const isRtl = computed(() => locale.value === 'ar')

/** on Select variant **/
const updateVariant = () => {
	const varianceIds = product?.variationAttributes.map(variant => variant.value?.attributeOptionId)
	const varianceSlug = product.variationAttributes
		.map(item => item.value?.variance?.slug)
		.filter(i => !!i)
		.join('-')

	emit('select:variant', {
		varianceSlug,
		varianceIds,
	})
}
</script>

<template>
	<div
		class="flex flex-col w-1/2 gap-4 max-md:w-full"
	>
		<div class="flex gap-4">
			<template v-if="loading">
				<Skeleton class="w-1/5 h-5" />
				<Skeleton class="w-1/5 h-5" />
				<Skeleton class="w-1/6 h-5" />
			</template>
			<template v-else>
				<div class="flex gap-1">
					<NuxtImg
						v-if="brandLogo"
						:src="brandLogo as string"
						class="h-5 w-auto"
						height="20"
						width="20"
						loading="eager"
						fit="contain"
						format="webp"
						:alt="brand?.name"
						:title="brand?.name"
						provider="backend"
					/>
					<span class="text-sm font-black">
						{{ brand?.name }}
					</span>
				</div>
				<div
					v-if="!!sku"
					class="flex"
				>
					<span class="text-sm font-bold">{{ $t('product.sku-title', { sku: sku }) }}</span>
				</div>
				<div class="flex gap-1 items-center justify-center">
					<template v-if="isAvailable">
						<span class="bg-green-600 rounded-full w-5 h-5 flex justify-center items-center">
							<Icon
								name="lucide:check"
								size="16px"
								class="text-white font-bold"
							/>
						</span>
						<span class="text-sm font-bold text-green-600">
							{{ $t('product.available') }}
						</span>
					</template>
				</div>
			</template>
		</div>
		<div class="flex text-xl gap-2 w-full flex-col">
			<template v-if="loading">
				<Skeleton class="w-full h-5" />
				<Skeleton class="w-full h-5" />
			</template>
			<template v-else>
				<h1 class="font-bold">
					{{ product?.variance?.name }}
				</h1>
			</template>
		</div>

		<div
			v-if="loading"
			class="flex items-center gap-2 flex-col"
		>
			<Skeleton class="w-full h-6" />
			<Skeleton class="w-full h-6" />
		</div>
		<div
			v-else
			class="flex items-center gap-2"
		>
			<div class="flex gap-1 items-center">
				<Icon
					name="ui:start-filled"
					size="14px"
					class="text-yellow-400 drop-shadow"
				/>
				<span class="text-sm font-semibold">{{ $t('product.rate-details', { rate: product?.avgRate }) }}</span>
			</div>

			<div class="flex">
				<div class="h-3 w-px bg-gray-300" />
			</div>

			<NuxtLink
				:to="rateLink"
				class="flex gap-1 items-center hover:underline"
			>
				<Icon
					name="lucide:message-square-more"
					size="12px"
				/>
				<span>{{ $t('product.write-comment') }}</span>
			</NuxtLink>
		</div>

		<div class="flex w-full border-y border-dashed border-gray-200" />

		<div
			v-if="loading"
			class="flex items-center gap-2 flex-col"
		>
			<Skeleton class="w-full h-6" />
			<Skeleton class="w-full h-6" />
		</div>
		<div
			v-else
			class="flex flex-col py-2 gap-4"
		>
			<price-card
				class="md:hidden sm:flex xs:hidden"
				:loading="loading"
				:product="product"
			/>
			<div class="flex items-center p-4 rounded-lg bg-sky-50 gap-2">
				<div class="flex items-center justify-center p-2 rounded bg-primary-500">
					<Icon
						name="lucide:truck"
						class="text-white"
					/>
				</div>

				<p class="text-sm ">
					{{ $t("product.delivery-text") }}
				</p>
			</div>
		</div>
		<div class="flex w-full border-y border-dashed border-gray-200" />

		<div
			v-if="loading"
			class="flex gap-2 flex-col"
		>
			<Skeleton class="w-1/3 h-6" />
			<Skeleton class="w-full h-6" />
			<Skeleton class="w-full h-6" />
		</div>
		<div
			v-for="variant in product?.variationAttributes"
			v-else
			:key="variant?.attributeId"
			class="flex flex-col w-full"
		>
			<div
				class="flex flex-col gap-2 py-4"
			>
				<div class="flex gap-2">
					<span class="text-sm font-medium">{{ variant.name }} : </span>
					<span class="text-sm font-bold text-primary-600">
						{{ variant.value.name }}
					</span>
				</div>
				<div class="flex gap-2 flex-wrap max-w-full">
					<div
						v-for="option in variant?.options"
						:key="option?.attributeOptionId"
						:class="{ 'not-in-stock': !option?.stock }"
						class="option flex items-center justify-center rounded border-primary-500"
					>
						<template v-if="variant.type === 'color'">
							<NuxtLink
								:class="{ 'border-primary-600 bg-gray-100': option?.attributeOptionId === variant.value?.attributeOptionId }"
								class="variant flex rounded-lg border-2 border-gray-200 p-2 justify-center items-center hover:border-primary-600"
								to="#"
								:title="option?.name"
								@click.prevent="() => {
									variant.value = option as any
									updateVariant()
								}"
							>
								<NuxtImg
									:src="option?.media?.gallery?.[0]?.preview"
									:title="`${product.name} - ${option?.name}`"
									:alt="`${product.name} - ${option?.name}`"
									class="variant-src w-14 h-14 object-contain"
									sizes="xs:100vw md:100px"
									width="100"
									height="100"
									format="webp"
									fit="contain"
									provider="backend"
								/>
							</NuxtLink>
						</template>
						<template v-else>
							<Button
								class="variant sm"
								:variant="`${option?.attributeOptionId === variant.value?.attributeOptionId?'outline':'secondary'}`"
							>
								<NuxtLink
									:title="option?.name"
									class="variant-src"
									to="#"
									@click.prevent="() => {
										variant.value = option as any
										updateVariant()
									}"
								>
									{{ option?.name }}
								</NuxtLink>
							</Button>
						</template>
					</div>
				</div>
			</div>

			<div class="flex w-full border-y border-dashed border-gray-200" />
		</div>

		<div
			v-if="loading"
			class="flex gap-2 flex-col"
		>
			<Skeleton class="w-1/3 h-6" />
			<Skeleton class="w-full h-6" />
			<Skeleton class="w-full h-6" />
		</div>
		<div
			v-else
			class="flex flex-col gap-4"
		>
			<h2 class="flex text-md font-medium">
				{{ $t('product.about-title') }}
			</h2>

			<div
				class="text-md line-clamp-6"
				v-html="product?.description"
			/>
		</div>

		<div
			v-if="loading"
			class="flex gap-4"
		>
			<Skeleton class="w-1/3 h-10" />
			<Skeleton class="w-1/3 h-10" />
			<Skeleton class="w-1/3 h-10" />
		</div>
		<div
			v-else
			class="flex gap-4"
		>
			<div
				v-for="variant in product?.variationAttributes"
				:key="`selected-${variant.attributeId}`"
				class="flex items-center bg-sky-50 py-2 px-4 rounded-lg flex-col"
			>
				<div class="flex gap-2">
					<Icon
						:name="`ui:${variant.icon || 'colors'}`"
						class="text-primary-600"
					/>

					<span class="text-sm font-semibold text-primary-500">{{ variant.name }}</span>
				</div>

				<div class="flex text-sm">
					{{ variant.value.name }}
				</div>
			</div>
		</div>

		<template v-if="loading">
			<div class="flex gap-2">
				<Skeleton class="w-44 h-6" />
				<Skeleton class="w-6 h-6" />
			</div>
		</template>
		<template v-else>
			<NuxtLink
				:to="detailsLink"
				class="flex w-full items-center gap-1 underline hover:text-primary-700"
			>
				<span class="text-md font-medium leading-none">{{ $t('product.see-more-details') }}</span>
				<Icon
					name="lucide:chevron-right"
					:class="{ 'rotate-180': isRtl }"
				/>
			</NuxtLink>
		</template>
	</div>
</template>

<style scoped lang="scss">
.not-in-stock {
  &.option {
    @apply overflow-hidden relative !important;
    .variant {
      @apply opacity-60 overflow-hidden;
      &::after {
        @apply content-[''] -rotate-45 absolute w-px h-[200%] z-10 bg-gray-600
      }
    }

  }
}
</style>
