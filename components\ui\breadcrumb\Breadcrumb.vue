<script lang="ts" setup>
import type { HTMLAttributes } from 'vue'
import {
	BreadcrumbItem,
	BreadcrumbList,
	BreadcrumbSeparator,
} from '~/components/ui/breadcrumb/index'

type Link = {
	name: string
	path?: string | null
	icon?: string
	isLast?: boolean
}

const props = defineProps<{
	class?: HTMLAttributes['class']
	links?: Link[]
	loading?: boolean
}>()

const { locale, t } = useI18n()
const isRtl = computed(() => locale.value === 'ar')
const breadcrumbs = computed<Link[]>(() => {
	return [
		{
			name: t('Main page'),
			path: '/',
		},
		...props.links,
	]
})
</script>

<template>
	<div
		class="flex bg-white z-1 py-4 px-4 rounded-b-lg"
	>
		<nav
			aria-label="breadcrumb"
			:class="props.class"
		>
			<BreadcrumbList>
				<template v-if="!loading">
					<div
						v-for="(link, index) in breadcrumbs"
						:key="`${link.path}-${index}`"
						class="inline-flex items-center"
					>
						<BreadcrumbItem>
							<NuxtLinkLocale
								:to="link.path"
								:class="{ 'text-primary-600 pointer-events-none': link.isLast }"
								class="inline-flex gap-2 items-center"
							>
								<Icon
									v-if="!index"
									name="lucide:house"
									size="13px"
								/>
								<span>{{ link.name }}</span>
							</NuxtLinkLocale>
						</BreadcrumbItem>
						<BreadcrumbSeparator
							v-if="!link.isLast"
							class="mx-2"
							:class="{ 'rotate-180': isRtl }"
						/>
					</div>
				</template>
				<template v-else>
					<div class="flex space-between w-96 gap-4 max-w-full">
						<Skeleton class="w-1/2 h-5" />
						<Skeleton class="w-1/3 h-5" />
						<Skeleton class="w-1/4 h-5 max-sm:hidden" />
					</div>
				</template>
			</BreadcrumbList>
		</nav>
	</div>
</template>
