<script setup lang="ts">
import { Skeleton } from '~/components/ui/skeleton'
import type { Banner } from '~/interfaces/banner/banner'
import type { NewArrival } from '~/interfaces/product/new-arrival'
import type { Item as Product } from '~/interfaces/product/product-list'

interface Props {
	name?: string
	products?: Product[] | NewArrival[] | undefined
	variant?: string
	loading?: boolean
	banner?: Banner
}
const { name, products = [], variant = 'default', loading = false, banner = null } = defineProps<Props>()

const imageSource = ref(null)
const fetching = ref(true)

/** Load source file image */
const initWidget = async () => {
	if (banner) {
		imageSource.value = banner?.media?.image?.preview
		fetching.value = false
	} else if (name) {
		const image = await import(`assets/widget/${name}.png`)
		return nextTick(() => {
			imageSource.value = image.default
			fetching.value = false
		})
	} else {
		fetching.value = false
	}
}

initWidget()
</script>

<template>
	<div
		:class="`widget-${variant}`"
		class="flex w-full h-full shadow bg-white rounded-lg"
	>
		<Skeleton
			v-if="fetching"
			class="w-full h-60"
		/>
		<template v-else>
			<slot name="default">
				<div class="image">
					<template v-if="loading">
						<Skeleton class="w-full h-ful" />
					</template>
					<slot name="image-text" />
					<slot name="image">
						<NuxtImg
							provider="backend"
							:src="imageSource"
							class="w-full h-full object-contain"
							:alt="$t('app.marketing-alt')"
							:title="$t('app.marketing-alt')"
							loading="lazy"
							format="webp"
							quality="90"
							width="415"
							height="460"
						/>
					</slot>
				</div>
				<div
					v-if="products.length"
					class="product"
				>
					<template v-if="loading">
						<div class="flex space-between w-96 h-full gap-4 max-w-full">
							<Skeleton class="w-1/2 h-24" />
							<div class="flex flex-col justify-between w-full">
								<Skeleton class="w-full h-10" />
								<Skeleton class="w-full h-10" />
							</div>
						</div>
						<div class="flex space-between w-96 h-full gap-4 max-w-full">
							<Skeleton class="w-1/2 h-24" />
							<div class="flex flex-col justify-between w-full">
								<Skeleton class="w-full h-10" />
								<Skeleton class="w-full h-10" />
							</div>
						</div>
					</template>
					<template
						v-for="product in products"
						v-else
						:key="product.id"
					>
						<ProductCard
							:product="product"
							variant="horizontal"
							:loading="!product?.productId"
						/>
					</template>
				</div>
			</slot>
		</template>
	</div>
</template>

<style scoped lang="scss">
.image {
  @apply w-full relative;
}

.image-object-cover {
  img {
    @apply object-cover rounded-lg;
  }
}

.widget-products {
  @apply grid gap-2 items-center justify-evenly py-6 px-4 md:grid-cols-2 xs:grid-cols-1 lg:grid-cols-3 ;
  .image {
    @apply flex items-center justify-center md:col-span-2 xs:col-span-1 lg:col-span-1 ;
    img {
      @apply h-24 w-80
    }
  }

  .product {
    @apply col-span-2 max-sm:flex-col gap-4 p-2 flex items-center justify-center ;
  }
}
</style>
