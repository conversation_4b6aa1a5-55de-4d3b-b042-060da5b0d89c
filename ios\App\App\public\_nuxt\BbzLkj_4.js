import{k as v,r as b,m as y,ar as x,l as k,i as D,d as L,e as d,t as p,q as M,o as g}from"./C3gyeM1K.js";const I=window.setInterval;function T(a){const{t}=v(),n=b(""),s=(e,o=null)=>o==="minutes"&&(e<5||e>10)?"other":o==="seconds"&&e<=10&&e>2?"few":o==="seconds"&&e<3||e===1?"one":e===2?"few":e>1?"many":"other",i=()=>{const e=new Date(a);if(isNaN(e.getTime())){n.value=t("time.expired");return}const o=Date.now(),r=e.getTime()-o;if(r<=0){n.value=t("time.expired");return}const u=Math.floor(r/(1e3*60*60*24)),l=Math.floor(r%(1e3*60*60*24)/(1e3*60*60)),f=Math.floor(r%(1e3*60*60)/(1e3*60)),m=Math.floor(r%(1e3*60)/1e3),h=t(`time.day.${s(u)}`,{count:u}),$=t(`time.hours.${s(l)}`,{count:l}),_=t(`time.minutes.${s(f,"minutes")}`,{count:f}),w=t(`time.seconds.${s(m,"seconds")}`,{count:m});n.value=[`${u} ${h}`,`${l} ${$}`,`${f} ${_}`,`${m} ${w}`].join(" : ")};let c=null;return y(()=>{i(),c=I(i,1e3)}),x(()=>{c&&clearInterval(c)}),k(()=>n.value)}const B={class:"flex items-center py-4 justify-center tabular-nums"},N={class:"text-sm"},j={class:"text-sm ps-2 tabular-nums"},P=D({__name:"count-down",props:{stock:{}},setup(a){var n;const t=T(new Date((n=a.stock)==null?void 0:n.unPublishedAt));return(s,i)=>(g(),L("div",B,[d("b",N,p(s.$t("product.offer-end-title")),1),d("span",j,p(M(t)),1)]))}});export{P as _};
