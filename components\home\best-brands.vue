<script setup lang="ts">
import BrandProductView from './brand-products.vue'
import type { Brand, BrandList } from '~/interfaces/brands/brand'

const { data, error, status } = useApi<BrandList>('brands', {
	query: {
		perPage: 4,
		labels: 'best-brands',
		orderBy: 'createdAt,desc',
	},
})

if (error.value) {
	console.error('Error fetching categories:', error.value)
}

const brands = computed(() => (data.value as BrandList)?.items as Brand[])
const loading = computed(() => status.value !== 'success')
</script>

<template>
	<Card class="col-span-3 max-sm:col-span-4">
		<CardHeader class="px-4 py-4">
			<h2 class="text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl">
				{{ $t('home.brand-title') }}
			</h2>
		</CardHeader>
		<CardContent class="grid gap-4 px-4 xs:grid-cols-1 lg:grid-cols-2">
			<template v-if="!!loading">
				<div
					v-for="(_, index) in Array(4)"
					:key="`brand-loading-${index}`"
					class="col-span-1 flex sm:min-h-64 xs:min-h-52  min-w-full gap-4"
				>
					<Skeleton class="w-full h-full" />
				</div>
			</template>
			<template v-else>
				<brand-product-view
					v-for="brand in brands"
					:key="brand.brandId"
					:brand="brand"
				/>
			</template>
		</CardContent>
	</Card>
</template>
