export interface CompareResponse {
	variances?: Variances[]
	attributes?: Attributes[]
}

export interface Variances {
	varianceId?: number
	productId?: number
	name?: string
	varianceName?: string
	slug?: string
	media?: Media
	stock?: Stock
}

export interface Media {
	gallery?: Gallery[]
}

export interface Gallery {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: never
}

export interface Stock {
	stockId?: number
	quantity?: number
	maxPerUser?: number
	supplierId?: number
	isOffer?: boolean
	priceBeforeOffer?: PriceBeforeOffer
	price?: Price
	unPublishedAt?: never
	isPreOrder?: boolean
	note?: string
}

export interface PriceBeforeOffer {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface Price {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface Attributes {
	attribute?: string
	attributeId?: number
	icon?: never
	attributeOptions?: AttributeOptions[]
}

export interface AttributeOptions {
	varianceId?: number
	attributeOptionId?: number
	name?: string
	value?: never
	attributeId?: number
}
