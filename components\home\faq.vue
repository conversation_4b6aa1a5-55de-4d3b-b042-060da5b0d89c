<script setup lang="ts">
interface Item {
	icon?: string
	viewBox?: string
	title?: string
	text?: string
}
const list: Item[] = [
	{
		icon: 'ui:faq-call',
		title: 'home.faq-call-title',
		text: 'home.faq-call-text',
	},

	{
		icon: 'ui:faq-tracking',
		title: 'home.faq-track-title',
		text: 'home.faq-track-text',
	},

	{
		icon: 'ui:faq-replace',
		title: 'home.faq-replace-title',
		text: 'home.faq-replace-text',
	},
	{
		icon: 'ui:faq-best-sel',
		title: 'home.faq-best-title',
		text: 'home.faq-best-text',
	},
	{
		icon: 'ui:faq-payments',
		title: 'home.faq-payment-title',
		text: 'home.faq-payment-text',
	},
]
</script>

<template>
	<div class="bg-gray-100 col-span-3 rounded-lg overflow-hidden p-6 shadow-md">
		<div class="faq w-full flex flex-col gap-10">
			<div class="w-full flex justify-center ">
				<h2 class="text-lg font-bold">
					{{ $t('home.faq-title') }}
				</h2>
			</div>
			<div class="w-full flex gap-6 justify-evenly items-center max-md:flex-wrap">
				<div
					v-for="(item, index) in list"
					:key="`faq-${index}`"
					class="col-span-1 flex flex-col items-center justify-center gap-4 max-w-56"
				>
					<div class="flex bg-primary-600 rounded-lg text-white p-2 shadow-lg">
						<Icon
							:name="item.icon"
							size="40px"
						/>
					</div>
					<div class="flex flex-col items-center justify-center text-center gap-1">
						<h3 class="text-lg font-bold text-gray-700">
							{{ $t(item.title) }}
						</h3>
						<span class="text-base font-normal text-gray-500 leading-tight">
							{{ $t(item.text) }}
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
.faq {
  @apply rounded-lg  p-2;
  background: url('/assets/images/faq-home-bg.png') no-repeat center center;
  background-size: 100%;
}
</style>
