<script setup lang="ts">
const { locale } = useI18n()
const switchLocalePath = useSwitchLocalePath()
</script>

<template>
	<div
		:key="locale"
		class="flex justify-center w-full bg-gray-100 will-change-transform"
		dir="ltr"
	>
		<div class="container flex justify-between gap-4 px-4 py-2 font-medium text-gray-500 xs:text-xs sm:text-md ">
			<div class="flex gap-4 items-center">
				<NuxtLinkLocale to="/faq">
					{{ $t("header.top-faq") }}
				</NuxtLinkLocale>
				<div class="flex w-px bg-gray-500 h-4" />
				<NuxtLinkLocale to="/about-us">
					{{ $t("header.top-about") }}
				</NuxtLinkLocale>
			</div>

			<div class="flex gap-4 items-center">
				<NuxtLink
					:class="locale == 'ar' ? 'hidden' : ''"
					:to="switchLocalePath('ar')"
					:replace="true"
				>العربي</NuxtLink>

				<NuxtLink
					:class="locale == 'en' ? 'hidden' : ''"
					:to="switchLocalePath('en')"
					:replace="true"
				>English</NuxtLink>
			</div>
		</div>
	</div>
</template>
