<script setup lang="ts">
const { t, locale } = useI18n()

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('header.top-about'))
})

const isRtl = computed(() => locale.value === 'ar')
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>

	<Card class="flex flex-col w-full h-full gap-2 my-6">
		<CardHeader class="relative text-center justify-center gap-4 text-white  rounded-lg !p-0">
			<NuxtImg
				src="/images/about/lg.png"
				sizes="1765px"
				width="1765"
				height="537"
				format="webp"
				quality="90"
				class="rounded-lg object-cover hidden lg:block w-full"
				alt="Large screen image"
				:preload="true"
			/>

			<NuxtImg
				src="/images/about/md.png"
				sizes="991px"
				width="991"
				height="296"
				format="webp"
				quality="90"
				class="rounded-lg object-cover hidden md:block lg:hidden w-full"
				alt="Medium screen image"
				:preload="true"
			/>

			<NuxtImg
				src="/images/about/sm.png"
				sizes="398px"
				width="398"
				height="296"
				format="webp"
				quality="90"
				class="rounded-lg object-cover block md:hidden w-full"
				alt="Small screen image"
				:preload="true"
			/>
			<div class="absolute inset-0 flex flex-col items-center justify-center text-white gap-4 px-4 max-sm:gap-0">
				<h1 class="font-bold text-2xl max-sm:text-sm">
					{{ $t('about-us.title') }}
				</h1>
				<h2 class="font-bold text-xl max-sm:text-xs">
					{{ $t('about-us.sub-title') }}
				</h2>
				<p class="text-base max-w-[75%] text-center max-sm:text-2xs max-sm:leading-tight">
					{{ $t('about-us.title-paragraph') }}
				</p>
			</div>
		</CardHeader>
		<CardContent class="flex flex-col gap-12 py-12">
			<div class="flex flex-col p-4 rounded-lg bg-sky-50 text-gray-600">
				<span class="text-lg font-bold">{{ $t('about-us.welcome-1') }}</span>
				<span class="text-base">{{ $t('about-us.welcome-2') }}</span>
			</div>

			<div class="flex flex-col">
				<span class="text-lg font-bold">{{ $t('about-us.story-title') }}</span>
				<span class="text-md">{{ $t('about-us.story-description') }}</span>
			</div>

			<div class="flex justify-between items-center gap-6 max-sm:flex-col">
				<div class="flex flex-col w-1/2 max-sm:w-full gap-2 cursor-pointer underline">
					<NuxtImg
						src="images/about-1.png"
						format="webp"
					/>
					<NuxtLinkLocale
						to="vision"
						class="flex items-center gap-2 text-sm text-gray-500 hover:text-primary-500"
					>
						{{ $t('about-us.vision-title') }}
						<Icon
							name="lucide:chevron-right"
							:class="{ 'rotate-180': isRtl }"
							size="15px"
						/>
					</NuxtLinkLocale>
				</div>
				<div class="flex flex-col w-1/2 max-sm:w-full gap-2 cursor-pointer underline">
					<NuxtImg
						src="images/about-2.png"
						format="webp"
					/>
					<NuxtLinkLocale
						to="services"
						class="flex items-center gap-2 text-sm text-gray-500  hover:text-primary-500"
					>
						{{ $t('about-us.service-title') }}
						<Icon
							name="lucide:chevron-right"
							:class="{ 'rotate-180': isRtl }"
							size="15px"
						/>
					</NuxtLinkLocale>
				</div>
			</div>
		</CardContent>
	</Card>
</template>
