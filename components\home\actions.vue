<script setup lang="ts">
import type { Categories } from '~/interfaces/category/categories'

const { data, error, status } = useApi<Categories[]>('categories', { // ?featured=1
	query: {
		isFeatured: 1,
		limit: 3,
		orderBy: 'createdAt,desc',
	},
})
const categories = computed<Categories[]>(() => (data.value as Categories[])?.slice(0, 3))
const loading = computed(() => status.value !== 'success')
if (error.value) {
	console.error('Error fetching most popular:', error.value)
}
</script>

<template>
	<Card class="col-span-1 max-md:col-span-3">
		<CardContent class="flex flex-col px-4">
			<div class="flex px-2 py-4">
				<h2 class="text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl">
					{{ $t('home.action-title') }}
				</h2>
			</div>

			<div class="grid md:grid-cols-2 sm:grid-cols-3 xs:grid-cols-2 gap-4 group">
				<template v-if="loading">
					<Card
						v-for="(_, index) in Array(3)"
						:key="`category-loading-${index}`"
						:class="{ 'md:col-span-2 sm:col-span-1 xs:col-span-2': !index }"
					>
						<Skeleton class="w-full h-48 bg-gray-200 rounded-lg" />
					</Card>
				</template>
				<template v-else>
					<NuxtLinkLocale
						v-for="(category, index) in categories"
						:key="category?.categoryId"
						:title="category.name"
						:to="`/category/${category.slug}`"
						:class="{ 'md:col-span-2 sm:col-span-1 xs:col-span-2': !index }"
					>
						<Card
							class="flex items-center justify-center  border border-gray-200 shadow-lg bg-gray-100 p-4 hover:border-primary-500"
						>
							<NuxtImg
								:src="category.cover?.src"
								:alt="category.name"
								:title="category.name"
								provider="backend"
								format="webp"
								quality="100"
								height="100%"
								class="object-cover h-40"
							/>
						</Card>
					</NuxtLinkLocale>
				</template>
			</div>
		</CardContent>
	</Card>
</template>
