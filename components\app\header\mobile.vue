<script setup lang="ts">
import { onClickOutside, useDebounceFn } from '@vueuse/core'
import { Button } from '~/components/ui/button'
import { Skeleton } from '~/components/ui/skeleton'
import type { Details } from '~/interfaces/product/details'
import { useCartStore } from '~/store/useCartStore'
import { useCompareStore } from '~/store/useCompareStore'

const localePath = useLocalePath()
const cartStore = useCartStore()
const { locale } = useI18n()
const emit = defineEmits<{
	(event: 'open:drawer', page: string): void
}>()

const router = useRouter()
const route = useRoute()
const keywords = ref(route.query?.q)
const isLoading = ref(false)
const products = ref<Details[]>([])
const isListOpen = ref(false)
const searchRef = ref(null)
const isRtl = computed(() => locale.value === 'ar')
const cartListCount = computed(() => cartStore.count)

const compareStore = useCompareStore()
const compareListCount = computed(() => compareStore.count || 0)

/** Update search result */
const debouncedFn = useDebounceFn(() => fetchSearch(), 1000)

/**
 * Handle on a Fetching search result
 * @returns {Promise<*>}
 */
const fetchSearch = async (): Promise<void> => {
	if (!(keywords.value as string)?.trim()) {
		return
	}

	const { $api } = useNuxtApp()
	return $api<Details[]>(`autocomplete?q=${keywords.value}&perPage=12`)
		.then((data) => {
			products.value = data || []
		})
		.catch((error) => {
			console.error(`ERROR Search, ${error}`)
		})
		.finally(() => {
			isLoading.value = false
			isListOpen.value = !!products.value.length
		})
}

/**
 * On Typing on the input
 */
const onTyping = () => {
	isListOpen.value = true
	products.value = []
	isLoading.value = true
	debouncedFn()
}

/** handle on search **/
const onSearchClicked = () => {
	isListOpen.value = !!products.value?.length
}

/**
 * Make sure the page not scrolling on case the menu list open
 */
watch(() => isListOpen.value, (value) => {
	if (import.meta.client) {
		document.body.style.overflowY = value ? 'hidden' : 'auto'
	}
}, { immediate: true })

/**
 * Handle on click outside a list
 */
onClickOutside(searchRef, () => {
	isListOpen.value = false
})

/**
 * On click the search result
 * Set the search field value by the selected products name
 */
const onSelectSearch = (product: Details) => {
	keywords.value = product?.name ?? '' as string
	const localePath = useLocalePath()
	const router = useRouter()
	isListOpen.value = false
	nextTick(() => {
		document.body.style.overflowY = 'auto'
		router.push(localePath(`/product/${product.slug}`))
	})
}

onUnmounted(() => {
	isListOpen.value = false
})

const clearSearch = () => {
	keywords.value = ''
	isListOpen.value = false
	document.body.style.overflowY = 'auto'
}

const openComparePage = () => {
	if (compareListCount.value <= 1) {
		return false
	}

	const ids = compareStore.products.join('/')
	router.push(localePath(`/compare/${ids}`))
}
</script>

<template>
	<div class="hidden w-full flex-col max-sm:flex pt-4">
		<div class="flex w-full items-center justify-between px-4">
			<NuxtLinkLocale to="/">
				<NuxtImg
					class="h-10"
					:alt="$t('app.action-mobile-logo')"
					:title="$t('app.action-mobile-logo')"
					src="/images/logo.png"
					loading="eager"
					format="webp"
					width="100"
					height="53"
				/>
			</NuxtLinkLocale>
			<div class="flex gap-2">
				<Button
					v-if="false"
					variant="icon"
					class="bg-gray-100"
					as-child
				>
					<NuxtLinkLocale to="/notifications">
						<Icon name="lucide:bell" />
					</nuxtlinklocale>
				</Button>

				<Button
					variant="icon"
					class="relative"
					:class="{ 'bg-primary-300 !text-primary-600': !!compareListCount }"
					@click="openComparePage"
				>
					<Icon
						name="ui:compare"
						size="20px"
					/>
					<Badge
						class="absolute top-1.5 right-1.5 !bg-primary-600 px-1 pointer-events-none"
						:class="{ hidden: !compareListCount }"
					>
						<span class="text-2xs font-semibold text-white leading-none">{{ compareListCount }}</span>
					</Badge>
				</Button>

				<Button
					variant="icon"
					class="bg-gray-100"
					as-child
				>
					<NuxtLinkLocale to="/my/profile">
						<Icon name="lucide:circle-user" />
					</NuxtLinkLocale>
				</Button>

				<Button
					variant="icon"
					class="bg-gray-100 relative"
					:class="{ 'bg-primary-300 !text-primary-600': cartListCount }"
					@click="emit('open:drawer', 'cart')"
				>
					<Icon name="lucide:shopping-cart" />
					<Badge
						class="absolute top-1.5 right-1.5 !bg-primary-600 px-1 pointer-events-none"
						:class="{ hidden: !cartListCount }"
					>
						<span class="text-2xs font-semibold text-white leading-none">{{ cartListCount }}</span>
					</Badge>
				</Button>
			</div>
		</div>
		<div class="flex flex-col w-full items-center">
			<div class="p-4 w-full">
				<div class="relative flex w-full gap-2 border rounded-lg border-gray-200 p-2">
					<Icon
						name="lucide:search"
						class="text-gray-400 text-2xl my-auto"
					/>
					<input
						v-model="keywords"
						name="keywords"
						type="text"
						autocomplete="off"
						class="outline-none w-full h-full"
						:placeholder="$t('header.search-placeholder')"
						@click="onSearchClicked"
						@input="onTyping"
					>
					<div
						v-if="keywords"
						role="button"
						class="flex flex-col absolute left-0 top-0 px-2 py-3 items-center justify-center"
						@click="clearSearch"
					>
						<Icon
							name="lucide:circle-x"
							size="20px"
							class="bg-gray-400 rounded-full text-gray-900"
						/>
					</div>
				</div>
			</div>
			<div
				v-if="isListOpen"
				class="relative w-full z-40 top-4"
			>
				<div
					class="flex w-full flex-col min-h-dvh bg-white absolute left-0 overflow-auto -top-3"
				>
					<template v-if="isLoading">
						<div
							v-for="(_) in Array(5)"
							:key="`search-loading-${_}`"
							:class="{ 'bg-gray-50': _ % 2 === 0 }"
							class="grid grid-cols-[auto_1fr_auto] w-full gap-2 my-2 px-4 hover:bg-primary-100"
						>
							<Skeleton class="w-10 h-12" />
							<Skeleton class="w-full h-12" />
							<div class="flex flex-col justify-center items-center">
								<Skeleton class="w-6 h-4" />
							</div>
						</div>
					</template>

					<template
						v-for="(product, index) in products"
						v-else
						:key="`search-${product.productId}`"
					>
						<div
							role="button"
							:class="{ 'bg-gray-50': index % 2===0 }"
							class="grid grid-cols-[auto_1fr_auto] w-full items-center gap-2 px-4 hover:bg-primary-100"
							@click="onSelectSearch(product)"
						>
							<div class="flex p-2 rounded-lg col-span-1">
								<NuxtImg
									:src="product.media?.cover?.[0]?.src"
									class="object-cover w-12 h-12"
									provider="backend"
									format="webp"
								/>
							</div>
							<span class="col-span-1 text-sm">{{ product.name }}</span>
							<div class="col-span-1 flex flex-col justify-center items-center">
								<Icon
									name="lucide:chevron-right"
									size="18px"
									:class="{ 'rotate-180': isRtl }"
								/>
							</div>
						</div>
					</template>
				</div>
			</div>
		</div>
	</div>
</template>
