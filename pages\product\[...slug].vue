<script setup lang="ts">
import { toast } from 'vue-sonner'
import type { Details, RedirectResponse, UseProductDetails } from '~/interfaces/product/details'
import { useCompareStore } from '~/store/useCompareStore'

interface PyNowResponse {
	orderId?: number
}

const url = useRequestURL()
const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const localePath = useLocalePath()
const slug = computed(() => (route.params?.slug as string[]).join('/'))
const { data, error, status } = await useApi<Details>(`products/${slug.value}`)

const response = computed<Details>(() => data.value as Details)

const product = ref<UseProductDetails>(useProduct(response.value as Details))

watch(response, (newProduct: Details | null) => {
	if (newProduct) {
		product.value = useProduct(newProduct as Details) as UseProductDetails
	}
}, { deep: true, immediate: true })

const loading = computed(() => status.value !== 'success')
const selectedProduct = ref(null)
const isPaying = ref(false)

/**
 * Redirect to correct product slug
 */
const redirectToCorrectProduct = () => {
	if ('details' in response.value) {
		navigateTo(`/product/${(response.value as RedirectResponse).details.redirectUrl}`, { redirectCode: 301 })
	}
}

/**
 * Redirect to error page
 */
const redirectToErrorPage = () => {
	throw createError({
		statusCode: 404,
		fatal: true,
		unhandled: false,
	})
}

if ((response.value as RedirectResponse)?.code === 301) {
	redirectToCorrectProduct()
}

if (response.value?.code === 404) {
	redirectToErrorPage()
}

if (error.value) {
	console.error(`Error: in fetching product details`, error.value)
	if (error.value?.statusCode === 404) {
		redirectToErrorPage()
	}
}

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(product.value?.name)
})
/** check the scrolling for details **/
useScrollTo()

/**
 * On Select the variants
 * @param selected
 */
const onSelectVariant = async ({ varianceSlug }) => {
	const productSlug = Array.isArray(route.params.slug) ? route.params.slug[0] : route.params.slug
	return nextTick(() => {
		router.push({
			name: route.name,
			params: {
				...route.params,
				slug: [
					productSlug,
					varianceSlug,
				],
			},
		})
	})
}

/**
 * Handle on create new order
 */
const onPayNow = async (quantity: number) => {
	const { $api } = useNuxtApp()
	isPaying.value = true
	return $api<PyNowResponse>('/orders/buy-now', {
		method: 'POST',
		body: {
			quantity,
			productId: product.value?.productId,
			varianceId: product.value?.variance?.varianceId,
		},
	})
		.then(async (data) => {
			await navigateTo(localePath(`/checkout/${data.orderId}/1`))
		})
		.catch((error) => {
			console.error(error)
			toast.error(error?.message || 'Something went wrong')
		})
		.finally(() => {
			nextTick(() => isPaying.value = false)
		})
}
const compareStore = useCompareStore()

/**
 * Handle on Compare product
 */
const onCompare = async (varianceId: number) => {
	if (!compareStore.products.includes(varianceId)) {
		return compareStore.setProduct(varianceId)
	}

	return compareStore.removeProduct(varianceId)
}

const canonical = computed(() => {
	return `${url.origin}${localePath(`/products/${route.params?.slug[0]}`)}`
})

const metaImage = computed(() => {
	return response.value?.media?.gallery?.[0]?.preview
})

definePageMeta({
	title: 'Product',
})

useSeoMeta({
	title: () => `${product.value?.metaTitle} | ${t('header.meta-site-name')}`,
	description: () => product.value?.metaDescription,
	ogTitle: () => `${product.value?.metaTitle} | ${t('header.meta-site-name')}`,
	ogDescription: () => product.value?.metaDescription,
	twitterTitle: () => `${product.value?.metaTitle} | ${t('header.meta-site-name')}`,
	twitterDescription: () => product.value?.metaDescription,
	ogImage: metaImage,
	twitterImage: metaImage,
})

useHead({
	link: [
		{
			rel: 'canonical',
			href: canonical.value,
		},
		{
			rel: 'twitter:card',
			href: url.href,
		},
		{
			rel: 'og:url',
			href: url.href,
		},
	],

	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': canonical.value,
				'url': canonical.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionmobile11',
					'https://www.instagram.com/actionwebsite/',
					canonical.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})

const varianceId = computed(() => product.value?.variance?.varianceId ?? (new Date()).getTime())
</script>

<template>
	<Card
		:key="varianceId"
		class="-mt-2 pt-2"
		:data-variance-id="varianceId"
	>
		<Breadcrumb
			:links="breadCrumbLinks"
			:loading="loading"
			class="!border-0 !shadow-none"
		/>

		<CardContent>
			<div
				class="flex gap-4 py-2 max-sm:flex-col"
			>
				<!-- Gallery -->
				<ProductGallery
					:product="product as UseProductDetails"
					:loading="loading"
				/>

				<div class="flex gap-4 max-md:flex-col w-full max-md:w-1/2 max-sm:w-full">
					<!-- Description -->

					<ProductDetails
						:product="product as UseProductDetails"
						:loading="loading"
						@select:variant="onSelectVariant"
					/>

					<!-- Sticky Payment Section on Mobile view -->
					<ProductPaymentCardMobile
						:product="product as UseProductDetails"
						:loading="loading"
						:is-paying="isPaying"
						@confirm:add-to-cart="selectedProduct = $event"
						@product:pay-now="onPayNow"
						@product:compare="onCompare"
					/>

					<!-- Payment Section -->
					<ProductPaymentCard
						:product="product as UseProductDetails"
						:loading="loading"
						:is-paying="isPaying"
						@confirm:add-to-cart="selectedProduct = $event"
						@product:pay-now="onPayNow"
						@product:compare="onCompare"
					/>
				</div>
			</div>
		</CardContent>
	</Card>

	<template v-if="product?.productId">
		<ProductSuggestions
			:product-id="product?.productId"
		/>
		<ProductInfo
			:loading="loading"
			:product="product as UseProductDetails"
		/>

		<Modal
			v-if="!!selectedProduct"
			:title="$t('cart-list.add-item-title')"
			@close="selectedProduct = null"
		>
			<template #footer>
				<div class="flex w-full justify-end items-center gap-4">
					<Button
						variant="outline"
						@click.once="selectedProduct = null"
					>
						<span>{{ $t('cart-list.confirm-continue') }}</span>
					</Button>

					<Button as-child>
						<NuxtLinkLocale to="/cart">
							<span>{{ $t('cart-list.confirm-continue-pay') }}</span>
						</NuxtLinkLocale>
					</Button>
				</div>
			</template>

			<template #body>
				<div class="flex w-full px-4 justify-center items-center mb-4">
					<DrawerCartListCard
						:product="selectedProduct"
						:view-only="true"
					/>
				</div>
			</template>
		</Modal>
	</template>
</template>
