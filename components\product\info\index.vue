<script setup lang="ts">
import ProductDescription from './description.vue'
import ProductRating from './rating.vue'
import type { Details } from '~/interfaces/product/details'

const { product, loading = false } = defineProps<{
	product?: Details | undefined
	loading?: boolean
}>()

const route = useRoute()

const overviewLink = computed(() => `${route.path}?tab=overview#details`)
const ratingLink = computed(() => `${route.path}?tab=rating#details`)
const activeTab = computed(() => route.query?.tab ?? 'overview')
</script>

<template>
	<Card id="details">
		<CardHeader class="flex items-center py-0 px-0 w-full flex-row gap-x-4 border-b-2 border-gray-200">
			<template v-if="loading">
				<div class="flex py-4 w-1/2 gap-6 px-2 -bottom-[2px] relative">
					<Skeleton class="w-1/5 h-8" />
					<Skeleton class="w-1/5 h-8" />
				</div>
			</template>
			<template v-else>
				<NuxtLink
					:to="overviewLink"
					class="text-xl font-bold text-gray-500 border-b-2 leading-[60px] relative -bottom-[2px] px-5"
					:class="{ 'text-primary-600 border-primary-600': activeTab == 'overview' }"
				>
					<h2>{{ $t('product.overview-title') }}</h2>
				</NuxtLink>
				<NuxtLink
					:to="ratingLink"
					class="text-xl font-bold text-gray-500 border-b-2 leading-[60px] relative -bottom-[2px] px-5"
					:class="{ 'text-primary-600 border-primary-600': activeTab == 'rating' }"
				>
					<h2>{{ $t('product.client-rating-title') }}</h2>
				</NuxtLink>
			</template>
		</CardHeader>
		<CardContent class="p-0">
			<ProductDescription
				v-if="activeTab == 'overview'"
				:product="product"
				:loading="loading"
			/>

			<ProductRating
				v-if="activeTab == 'rating'"
				:product="product"
				:loading="loading"
			/>
		</CardContent>
	</Card>
</template>
