<script setup lang="ts">
type linkType = {
	title: string
	path: string
}

type Image = {
	path: string
	name: string
}

const year = ref((new Date()).getFullYear())
const images = ref<Image[]>([])
const paymentsIcons = ref([
	'footer-apple-pay',
	'footer-cash-on-delivery',
	'footer-click',
	'footer-google-pay',
	'footer-master',
	'footer-visa',
])
const links = ref<linkType[]>([
	{
		title: 'footer.privacy',
		path: '/privacy',
	},
	{
		title: 'footer.usage',
		path: '/website-usage',
	},
	{
		title: 'footer.wallet',
		path: '/wallet-usage',
	},
	{
		title: 'footer.terms',
		path: '/service-usage',
	},
])

const fetchImages = () => {
	paymentsIcons.value.forEach((name) => {
		images.value.push({
			name: name.split('-').filter(i => i !== 'footer').join(' '),
			path: `/images/${name}.png`,
		})
	})

	return images.value
}

fetchImages()
</script>

<template>
	<div class="flex w-full justify-center items-center min-h-20 shadow-lg border-t border-gray-100 bg-white max-sm:bg-gray-50">
		<div
			class="grid md:grid-cols-3 md:gap-4 container xs:grid-cols-1 xs:gap-5 xs:py-4"
		>
			<div class="col-span-1 flex max-md:justify-center">
				<span class="text-sm font-normal text-gray-500">
					{{ $t('footer.copy-right', { year }) }}
				</span>
			</div>
			<div class="col-span-1 flex gap-2 items-center justify-center">
				<template
					v-for="(item, itemIndex) in images"
					:key="`footer-payment-${itemIndex}`"
				>
					<NuxtImg
						:src="item.path"
						loading="lazy"
						class=""
						width="25"
						height="12"
						fit="fill"
						quality="100"
						:alt="item.name"
						:title="item.name"
					/>
				</template>
			</div>
			<div class="col-span-1 flex items-center justify-center gap-x-5 gap-y-2 flex-wrap">
				<NuxtLinkLocale
					v-for="(link, index) in links"
					:key="`footer-link-${index}`"
					class="flex"
					:to="link.path"
				>
					<span class="xs:text-sm md:text-xs text-gray-500">{{ $t(link.title) }}</span>
				</NuxtLinkLocale>
			</div>
		</div>
	</div>
</template>
