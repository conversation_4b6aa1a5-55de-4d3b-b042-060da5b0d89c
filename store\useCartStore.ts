import { defineStore } from 'pinia'
import type { Items, CartResponse, Total } from '~/interfaces/cart/cart-list'
import type { QuantityPayload } from '~/interfaces/payload'

export const useCartStore = defineStore('cart', {

	state: () => ({
		list: [] as Items[],
		total: {} as Total,
		fetching: true as boolean,
		error: null,
	}),

	actions: {
		async fetch(): Promise<void> {
			try {
				this.fetching = true
				const { data, error, status } = await useApi<CartResponse>('/cart')

				if (error.value) {
					return console.error('Error fetching cart:', error.value)
				}

				if (status.value === 'success') {
					this.setResponse(data.value as CartResponse)
				}

				this.fetching = false
			} catch (err) {
				console.error('Error in fetch:', err)
			}
		},

		async fetchMounted(): Promise<void> {
			const nuxtApp = useNuxtApp()
			return nuxtApp.$api<CartResponse>('/cart')
				.then((data) => {
					this.setResponse(data)
				})
				.catch((error: Error) => {
					console.error('Error fetching cart:', error)
				})
				.finally(() => {
					this.fetching = false
				})
		},

		async addToList(payload: QuantityPayload): Promise<void> {
			const nuxtApp = useNuxtApp()
			await nuxtApp.$api<CartResponse>('/cart', {
				method: 'POST',
				body: payload,
			}).then((data) => {
				this.setResponse(data)
			})
		},

		async removeFromList(cartId: number): Promise<void> {
			const { $api } = useNuxtApp()
			await $api<CartResponse>(`/cart/${cartId}`, {
				method: 'DELETE',
			}).then((data) => {
				this.setResponse(data)
			})
		},

		async updateQuantity(payload: QuantityPayload): Promise<void> {
			const { $api } = useNuxtApp()
			$api<CartResponse>('/cart', {
				method: 'PUT',
				body: payload,
			}).then((data) => {
				this.setResponse(data)
			}).catch((error) => {
				return console.error('Error in update cart:', error)
			}).finally(() => {
				this.fetching = false
			})
		},

		setResponse(response: CartResponse): void {
			this.list = response.items as Items[]
			this.total = response.total as Total
		},

		hasCart(productId: number): boolean {
			return this.list.some((item: Items) => item.productId == productId) as boolean
		},

		hasCartProduct(productId: number, varianceId: number): boolean {
			return this.list.some((item: Items) => item.productId == productId && item.varianceId == varianceId) as boolean
		},

		findCartId(productId: number): number | null {
			return this.list?.find((item: Items) => item.productId === productId)?.cartId || null
		},
	},

	getters: {
		listIds: ({ list }): number[] => list.map((item: Items) => item.productId),
		count: ({ list }): number => list?.length,
	},
})

if (import.meta.hot) {
	import.meta.hot.accept(acceptHMRUpdate(useCartStore, import.meta.hot))
}
