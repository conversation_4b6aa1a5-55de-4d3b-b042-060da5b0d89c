import { defineStore } from 'pinia'
import { useUserToken } from '~/composables/useUserToken'
import type { Phone, User, UserAuth } from '~/interfaces/auth/auth'
import type {
	AuthLoginPayload,
	AuthRegisterPayload,
	AuthPassPayload,
	AuthPhone, AuthResetPassPayload,
} from '~/interfaces/auth/form'

export const useAuthStore = defineStore('auth', {
	state: () => ({
		user: null as User | unknown,
		token: null as string | null,
		forgotForm: {
			userData: null as User | null,
			phone: null as Phone | null,
		},
	}),

	actions: {

		setUserSession(session: UserAuth): void {
			const { setToken } = useUserToken()
			setToken(session.accessToken as string)
			this.user = session.user as User
			this.token = session.accessToken as string
		},

		removeUserSession(): void {
			const { setToken } = useUserToken()
			setToken(null)
			this.user = null
			this.token = null
		},

		async login(payload: AuthLoginPayload): Promise<void> {
			const { $api } = useNuxtApp()
			return $api<UserAuth>('/auth/login', {
				method: 'POST',
				body: payload,
			}).then((data) => {
				// set user data in storage and store
				this.setUserSession(data as UserAuth)
				const localePath = useLocalePath()
				navigateTo(localePath('/my/profile/'))
			}).catch((error) => {
				throw error
			})
		},

		async forgotPasswordPhone(phone: Phone): Promise<User> {
			const { $api } = useNuxtApp()
			return $api<User>('/auth/forgot-password-phone', {
				method: 'POST',
				body: {
					phone,
				},
			})
		},

		async resetVerifyPhoneOTP(payload: { code: string, userId: number }): Promise<void> {
			const { $api } = useNuxtApp()
			return $api('/auth/verification-code', {
				method: 'POST',
				body: payload,
			})
		},

		async resetPassword(payload: AuthResetPassPayload): Promise<void> {
			const { $api } = useNuxtApp()
			return $api('/auth/reset-password-code', {
				method: 'POST',
				body: payload,
			})
		},

		async logout(): Promise<void> {
			const { $api } = useNuxtApp()
			return $api('auth/logout', {
				method: 'DELETE',
			}).then(() => {
				// remove user data in storage and store
				this.removeUserSession()
				this.user = null
				const localePath = useLocalePath()
				navigateTo(localePath('/'))
			}).catch((error) => {
				throw error
			})
		},

		async signup(payload: AuthRegisterPayload): Promise<void> {
			const { $api } = useNuxtApp()
			return $api('/auth/register', {
				method: 'POST',
				body: payload,
			})
				.then(() => {
					// login now with the credential
					return this.login({
						password: payload.password,
						phone: payload.phone,
					} as AuthLoginPayload)
				})
				.catch((error) => {
					throw error
				})
		},

		async uploadAvatar(media: unknown): Promise<void> {
			const { $api } = useNuxtApp()
			return $api('/my/update-image', {
				method: 'POST',
				body: { media },
			}).then(() => {
				this.fetchUser()
			}).catch((error) => {
				throw error
			})
		},

		async changePassword(payload: AuthPassPayload): Promise<void> {
			const { $api } = useNuxtApp()
			return $api<never>('/my/update-password', {
				method: 'PUT',
				body: payload,
			})
		},

		async changeEmail(email: string): Promise<void> {
			const { $api } = useNuxtApp()
			return $api<never>('/my/update-email', {
				method: 'PUT',
				body: {
					...this.user as UserAuth,
					email,
				},
			})
		},

		async changePhone(phone: AuthPhone): Promise<void> {
			const { $api } = useNuxtApp()
			return $api<never>('/my/update-phone-number', {
				method: 'PUT',
				body: {
					...this.user as UserAuth,
					phone,
				},
			})
		},

		async verifyPhoneOTP(otp: string): Promise<void> {
			const { $api } = useNuxtApp()
			return $api<never>('/my/verify-otp', {
				method: 'POST',
				body: { otp },
			})
		},

		async fetchUser(): Promise<void> {
			const { $api } = useNuxtApp()
			return $api<UserAuth>('/my/profile').then((data) => {
				this.user = data
			})
		},
	},

	getters: {
		isLoggedIn: () => {
			const { userToken } = useUserToken()
			return !!userToken.value
		},
		userSession: ({ user }) => user as User,
	},
})

if (import.meta.hot) {
	import.meta.hot.accept(acceptHMRUpdate(useAuthStore, import.meta.hot))
}
